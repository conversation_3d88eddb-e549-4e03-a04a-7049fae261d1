# Common 通用基础模块

## 模块概述

Common模块是HarmonyOS应用的通用基础模块，提供了一套完整的工具类、UI组件、数据模型和常量定义。该模块采用模块化设计，为应用开发提供了丰富的基础功能支持，包括窗口管理、日志记录、数据存储、响应式布局、网络处理等核心功能。

## 模块结构

```
Common/
├── Index.ets                 # 模块导出入口文件
├── src/main/ets/
│   ├── component/            # UI组件
│   │   ├── TopNavigationView.ets    # 顶部导航栏组件
│   │   ├── WebSheet.ets             # Web弹窗组件
│   │   ├── LoadingMore.ets          # 加载更多组件
│   │   ├── NoMore.ets               # 无更多内容组件
│   │   └── VideoComponent.ets       # 视频播放组件
│   ├── constant/             # 常量定义
│   │   ├── CommonConstants.ets      # 通用常量
│   │   ├── CommonEnums.ets          # 通用枚举
│   │   └── ErrorCodeConstants.ets   # 错误码常量
│   ├── model/                # 数据模型
│   │   ├── GlobalInfoModel.ets      # 全局信息模型
│   │   ├── BundleInfoData.ets       # 应用包信息模型
│   │   ├── ResponseData.ets         # 响应数据模型
│   │   └── LoadingModel.ets         # 加载状态模型
│   ├── util/                 # 工具类
│   │   ├── WindowUtil.ets           # 窗口管理工具
│   │   ├── Logger.ets               # 日志工具
│   │   ├── GlobalContext.ets        # 全局上下文管理
│   │   ├── BreakpointSystem.ets     # 响应式断点系统
│   │   ├── WebUtil.ets              # Web工具
│   │   ├── NetworkUtil.ets          # 网络工具
│   │   ├── ColorUtil.ets            # 颜色工具
│   │   ├── ImageUtil.ets            # 图片工具
│   │   └── ...                      # 其他工具类
│   ├── view/                 # 视图组件
│   │   ├── LoadingView.ets          # 加载视图
│   │   ├── LoadingFailedView.ets    # 加载失败视图
│   │   ├── EmptyContentView.ets     # 空内容视图
│   │   └── NoNetworkView.ets        # 无网络视图
│   ├── viewmodel/            # 视图模型
│   │   ├── BaseVM.ets               # 基础视图模型
│   │   ├── BaseState.ets            # 基础状态类
│   │   └── BaseVMEvent.ets          # 基础视图模型事件
│   ├── storagemanager/       # 存储管理
│   │   ├── PreferenceManager.ets    # 首选项管理器
│   │   └── MockRequest.ets          # 模拟请求工具
│   ├── routermanager/        # 路由管理
│   │   └── PageContext.ets          # 页面上下文
│   └── updateservice/        # 更新服务
│       └── UpdateService.ets        # 应用更新服务
```

## 核心功能模块

### 1. 窗口管理 (WindowUtil)

提供窗口相关的管理功能，包括状态栏设置、全屏模式、屏幕方向等。

```typescript
import { WindowUtil, StatusBarColorType, ScreenOrientation } from '@ohos/common';

// 更新状态栏颜色
WindowUtil.updateStatusBarColor(context, isDark);

// 设置全屏模式
WindowUtil.setFullScreen(context, isFullScreen);

// 设置屏幕方向
WindowUtil.setScreenOrientation(context, ScreenOrientation.PORTRAIT);
```

### 2. 日志系统 (Logger)

统一的日志输出工具，封装HarmonyOS的hilog功能。

```typescript
import { Logger } from '@ohos/common';

// 创建日志实例
const logger = new Logger('[MyModule]');

// 输出不同级别的日志
logger.debug('调试信息');
logger.info('普通信息');
logger.warn('警告信息');
logger.error('错误信息');
```

### 3. 响应式断点系统 (BreakpointSystem)

管理应用的响应式布局断点，根据屏幕尺寸自动调整UI。

```typescript
import { BreakpointSystem, BreakpointType, BreakpointTypeEnum } from '@ohos/common';

// 获取断点系统实例
const breakpointSystem = BreakpointSystem.getInstance();

// 定义响应式值
const responsiveWidth = new BreakpointType({
  sm: 100,    // 小屏幕
  md: 200,    // 中屏幕
  lg: 300,    // 大屏幕
  xl: 400     // 超大屏幕
});

// 根据当前断点获取值
const currentWidth = responsiveWidth.getValue(BreakpointTypeEnum.MD);
```

### 4. 全局上下文管理 (GlobalContext)

提供全局对象存储和管理功能。

```typescript
import { GlobalContext } from '@ohos/common';

// 获取全局上下文实例
const globalContext = GlobalContext.getContext();

// 存储对象
globalContext.setObject('myKey', myObject);

// 获取对象
const myObject = globalContext.getObject('myKey');

// 删除对象
globalContext.deleteObject('myKey');
```

### 5. 数据存储管理 (PreferenceManager)

提供应用数据持久化存储功能。

```typescript
import { PreferenceManager } from '@ohos/common';

// 获取首选项管理器实例
const preferenceManager = PreferenceManager.getInstance();

// 存储数据
await preferenceManager.setValue('userSettings', { theme: 'dark', language: 'zh' });

// 获取数据
const userSettings = await preferenceManager.getValue<UserSettings>('userSettings');

// 删除数据
await preferenceManager.deleteValue('userSettings');
```

## UI组件使用

### 1. 顶部导航栏 (TopNavigationView)

标准的顶部导航栏组件，支持返回按钮、标题和菜单区域。

```typescript
import { TopNavigationView, TopNavigationData } from '@ohos/common';

@Component
struct MyPage {
  @State navigationData: TopNavigationData = new TopNavigationData();

  aboutToAppear() {
    this.navigationData.title = '页面标题';
    this.navigationData.showBackButton = true;
  }

  @Builder
  menuView() {
    // 自定义菜单内容
    Text('菜单')
  }

  build() {
    Column() {
      TopNavigationView({
        topNavigationData: this.navigationData,
        menuView: this.menuView
      })
      // 页面内容
    }
  }
}
```

### 2. Web弹窗组件 (WebSheet)

用于显示Web内容的弹窗组件。

```typescript
import { WebSheetBuilder, WebUrlType } from '@ohos/common';

// 显示Web弹窗
WebSheetBuilder.show({
  url: 'https://example.com',
  urlType: WebUrlType.EXTERNAL,
  title: 'Web页面'
});
```

### 3. 加载状态视图

提供多种加载状态的视图组件。

```typescript
import { LoadingView, LoadingFailedView, EmptyContentView, NoNetworkView } from '@ohos/common';

@Component
struct ContentPage {
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = 
    AppStorage.get('GlobalInfoModel')!;

  build() {
    Column() {
      if (this.isLoading) {
        LoadingView(this.globalInfoModel.currentBreakpoint)
      } else if (this.hasError) {
        LoadingFailedView()
      } else if (this.isEmpty) {
        EmptyContentView()
      } else if (!this.hasNetwork) {
        NoNetworkView()
      } else {
        // 正常内容
      }
    }
  }
}
```

## 工具类使用

### 网络工具 (NetworkUtil)

```typescript
import { NetworkUtil } from '@ohos/common';

// 检查网络连接状态
const isConnected = await NetworkUtil.isNetworkConnected();

// 获取网络类型
const networkType = await NetworkUtil.getNetworkType();
```

### 颜色工具 (ColorUtil)

```typescript
import { ColorUtil } from '@ohos/common';

// 颜色格式转换
const hexColor = ColorUtil.rgbToHex(255, 0, 0);
const rgbColor = ColorUtil.hexToRgb('#FF0000');
```

### 图片工具 (ImageUtil)

```typescript
import { ImageUtil } from '@ohos/common';

// 图片处理
const compressedImage = await ImageUtil.compressImage(imageData, 0.8);
const resizedImage = await ImageUtil.resizeImage(imageData, 300, 200);
```

## 常量和枚举

### 通用常量 (CommonConstants)

```typescript
import { CommonConstants } from '@ohos/common';

// 使用预定义常量
const navigationHeight = CommonConstants.NAVIGATION_HEIGHT;
const fullPercent = CommonConstants.FULL_PERCENT;
const spacing = CommonConstants.SPACE_16;
```

### 通用枚举 (CommonEnums)

```typescript
import { LoadingStatus, ModuleNameEnum, ScrollDirectionEnum } from '@ohos/common';

// 使用枚举值
const status = LoadingStatus.LOADING;
const module = ModuleNameEnum.HOME;
const direction = ScrollDirectionEnum.UP;
```

## 安装和配置

1. 在项目的`oh-package.json5`中添加依赖：

```json
{
  "dependencies": {
    "@ohos/common": "file:../Common"
  }
}
```

2. 在需要使用的模块中导入：

```typescript
import { Logger, WindowUtil, GlobalContext } from '@ohos/common';
```

## 注意事项

1. **单例模式**：多个工具类采用单例模式，确保全局唯一实例
2. **响应式设计**：组件和工具支持多断点响应式布局
3. **类型安全**：所有API都提供完整的TypeScript类型定义
4. **性能优化**：组件支持懒加载和冻结优化
5. **错误处理**：提供完善的错误处理和日志记录机制

## 版本信息

- 版本：1.0.0
- 许可证：Apache-2.0
- 主入口：Index.ets

该模块为HarmonyOS应用开发提供了完整的基础设施支持，建议在项目开发中充分利用这些通用功能来提高开发效率和代码质量。
