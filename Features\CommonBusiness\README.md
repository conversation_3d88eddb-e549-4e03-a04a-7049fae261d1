# CommonBusiness 通用业务模块

## 模块概述

CommonBusiness模块是HarmonyOS应用的通用业务组件库，专门为应用的业务层提供可复用的UI组件、数据模型和视图模型。该模块基于Common基础模块构建，提供了横幅展示、卡片布局、详情页面、首页视图等常用业务组件，支持响应式设计和组件复用优化。

## 模块结构

```
Features/CommonBusiness/
├── Index.ets                 # 模块导出入口文件
├── src/main/ets/
│   ├── component/            # 业务UI组件
│   │   ├── BannerCard.ets           # 横幅卡片组件
│   │   ├── BannerItem.ets           # 横幅项组件
│   │   ├── BaseDetailComponent.ets  # 基础详情组件
│   │   ├── FullScreenNavigation.ets # 全屏导航组件
│   │   └── LoadingMoreItem.ets      # 加载更多项组件
│   ├── model/                # 业务数据模型
│   │   ├── BannerData.ets           # 横幅数据模型
│   │   ├── CardData.ets             # 卡片数据模型
│   │   ├── FullScreenNavigationData.ets # 全屏导航数据模型
│   │   ├── RouterParams.ets         # 路由参数接口
│   │   └── TabStatusBarModel.ets    # 标签状态栏模型
│   ├── view/                 # 业务视图组件
│   │   └── BaseHomeView.ets         # 基础首页视图
│   └── viewmodel/            # 业务视图模型
│       ├── BaseHomeViewModel.ets    # 基础首页视图模型
│       ├── BaseHomeState.ets        # 基础首页状态
│       └── BannerSource.ets         # 横幅数据源
```

## 核心业务组件

### 1. 横幅展示系统

#### BannerCard - 横幅卡片组件

支持响应式布局的横幅展示组件，在大屏设备上显示为水平滚动列表，在小屏设备上显示为轮播图。

```typescript
import { BannerCard, BannerData, BannerState } from '@ohos/commonbusiness';

@Component
struct HomePage {
  @State bannerState: BannerState = new BannerState();
  @State tabViewType: number = 0;

  build() {
    Column() {
      BannerCard({
        bannerState: this.bannerState,
        tabViewType: this.tabViewType,
        handleItemClick: (bannerData: BannerData) => {
          // 处理横幅点击事件
          console.log('Banner clicked:', bannerData.bannerTitle);
        }
      })
    }
  }
}
```

#### BannerItem - 横幅项组件

可复用的横幅项组件，支持组件复用优化。

```typescript
import { BannerItem, BannerData } from '@ohos/commonbusiness';

@Component
struct BannerList {
  @State bannerData: BannerData = new BannerData();

  aboutToAppear() {
    this.bannerData.bannerTitle = '标题';
    this.bannerData.bannerSubTitle = '副标题';
    this.bannerData.mediaUrl = 'banner_image.jpg';
  }

  build() {
    BannerItem({ bannerData: this.bannerData })
  }
}
```

### 2. 卡片布局系统

#### CardData - 卡片数据模型

定义了完整的卡片数据结构，支持多种卡片样式类型。

```typescript
import { CardData, CardStyleTypeEnum, CardTypeEnum, MediaTypeEnum } from '@ohos/commonbusiness';

// 创建卡片数据
const cardData = new CardData();
cardData.id = 1;
cardData.cardTitle = '卡片标题';
cardData.cardSubTitle = '卡片副标题';
cardData.cardType = CardTypeEnum.COMPONENT;
cardData.cardStyleType = CardStyleTypeEnum.PICTURE_ABOVE_LIST;
cardData.cardImage = 'card_image.jpg';
cardData.version = '1.0.0';

// 添加卡片内容
cardData.cardContents = [
  {
    id: 1,
    type: CardTypeEnum.COMPONENT,
    mediaType: MediaTypeEnum.IMAGE,
    mediaUrl: 'content_image.jpg',
    title: '内容标题',
    subTitle: '内容副标题',
    desc: '内容描述'
  }
];
```

#### 卡片样式类型

```typescript
import { CardStyleTypeEnum } from '@ohos/commonbusiness';

// 支持的卡片样式类型
CardStyleTypeEnum.PICTURE_ABOVE_LIST  // 图片在上方的列表样式
CardStyleTypeEnum.LIST                // 纯列表样式
CardStyleTypeEnum.PICTURE             // 纯图片样式
CardStyleTypeEnum.PICTURE_ABOVE_TEXT  // 图片在上方的文本样式
CardStyleTypeEnum.PICTURE_TO_SWIPER   // 图片转轮播样式
```

### 3. 页面视图组件

#### BaseHomeView - 基础首页视图

通用的首页视图组件，提供统一的加载状态管理和视图切换。

```typescript
import { BaseHomeView, LoadingModel, LoadingStatus } from '@ohos/commonbusiness';

@Component
struct HomePage {
  @State loadingModel: LoadingModel = new LoadingModel();

  aboutToAppear() {
    this.loadingModel.loadingStatus = LoadingStatus.LOADING;
    this.loadData();
  }

  @Builder
  contentView() {
    // 自定义内容视图
    Column() {
      Text('首页内容')
    }
  }

  @Builder
  topTitleView() {
    // 自定义标题视图
    Text('首页标题')
  }

  async loadData() {
    try {
      // 加载数据逻辑
      this.loadingModel.loadingStatus = LoadingStatus.SUCCESS;
    } catch (error) {
      this.loadingModel.loadingStatus = LoadingStatus.FAILED;
    }
  }

  build() {
    BaseHomeView({
      loadingModel: this.loadingModel,
      contentView: this.contentView,
      topTitleView: this.topTitleView,
      reloadData: () => {
        this.loadData();
      }
    })
  }
}
```

#### BaseDetailComponent - 基础详情组件

通用的详情页面组件，支持加载状态管理。

```typescript
import { BaseDetailComponent, LoadingStatus } from '@ohos/commonbusiness';

@Component
struct DetailPage {
  @State loadingStatus: LoadingStatus = LoadingStatus.LOADING;

  @Builder
  detailContentView() {
    // 详情内容视图
    Column() {
      Text('详情内容')
    }
  }

  @Builder
  topTitleView() {
    // 顶部标题视图
    Text('详情标题')
  }

  build() {
    BaseDetailComponent({
      loadingStatus: this.loadingStatus,
      detailContentView: this.detailContentView,
      topTitleView: this.topTitleView,
      reloadData: () => {
        // 重新加载数据
        this.loadingStatus = LoadingStatus.LOADING;
      }
    })
  }
}
```

### 4. 导航组件

#### FullScreenNavigation - 全屏导航组件

用于全屏页面的顶部导航栏组件，支持标题显示、标签页视图、模糊效果等功能。

```typescript
import { FullScreenNavigation, FullScreenNavigationData } from '@ohos/commonbusiness';

@Component
struct FullScreenPage {
  @State navigationData: FullScreenNavigationData = new FullScreenNavigationData();

  aboutToAppear() {
    this.navigationData.title = '全屏页面';
    this.navigationData.showBackButton = true;
    this.navigationData.showTab = true;
    this.navigationData.titleOffsetY = 0;
    this.navigationData.titleScale = 1;
  }

  @Builder
  tabView() {
    // 自定义标签视图
    Row() {
      Text('标签1')
      Text('标签2')
    }
  }

  build() {
    Column() {
      FullScreenNavigation({
        topNavigationData: this.navigationData,
        tabView: this.tabView
      })
      // 页面内容
    }
  }
}
```

## 视图模型系统

### BaseHomeViewModel - 基础首页视图模型

管理首页相关的业务逻辑和状态，支持泛型扩展。

```typescript
import { BaseHomeViewModel, BaseHomeState, BaseHomeEventType } from '@ohos/commonbusiness';

class MyHomeState extends BaseHomeState {
  // 扩展自定义状态
  public customData: string = '';
}

class MyHomeViewModel extends BaseHomeViewModel<MyHomeState> {
  constructor() {
    super();
    this.state = new MyHomeState();
  }

  // 重写事件处理方法
  protected handleEvent(eventType: BaseHomeEventType, param?: any): void {
    switch (eventType) {
      case BaseHomeEventType.REFRESH:
        this.refreshData();
        break;
      case BaseHomeEventType.LOAD_MORE:
        this.loadMoreData();
        break;
    }
  }

  private async refreshData() {
    // 刷新数据逻辑
  }

  private async loadMoreData() {
    // 加载更多数据逻辑
  }
}
```

## 路由参数接口

### 页面跳转参数

```typescript
import { 
  ComponentDetailParams, 
  SampleDetailParams, 
  ArticleDetailParams 
} from '@ohos/commonbusiness';

// 组件详情页面参数
const componentParams: ComponentDetailParams = {
  componentName: 'Button',
  componentId: 123
};

// 示例详情页面参数
const sampleParams: SampleDetailParams = {
  currentIndex: 0,
  sampleCardId: 456
};

// 文章详情页面参数
const articleParams: ArticleDetailParams = {
  id: 789,
  isArticle: true,
  title: '文章标题',
  detailsUrl: 'https://example.com/article/789',
  tabViewType: 0
};

// 页面跳转示例
router.pushUrl({
  url: 'pages/ComponentDetail',
  params: componentParams
});
```

## 标签栏配置

### TabBarType - 标签栏类型

```typescript
import { TabBarType, TAB_CONTENT_STATUSES } from '@ohos/commonbusiness';

// 标签栏类型
const currentTab = TabBarType.HOME;  // 首页
const sampleTab = TabBarType.SAMPLE; // 示例
const practiceTab = TabBarType.PRACTICE; // 实践
const mineTab = TabBarType.MINE;     // 我的

// 标签内容状态栏配置
const statusBarConfig = TAB_CONTENT_STATUSES[TabBarType.HOME]; // true表示深色状态栏
```

## 数据源管理

### BannerSource - 横幅数据源

```typescript
import { BannerSource, BannerData } from '@ohos/commonbusiness';

// 创建横幅数据源
const bannerSource = new BannerSource();

// 添加横幅数据
const bannerData = new BannerData();
bannerData.bannerTitle = '横幅标题';
bannerData.mediaUrl = 'banner.jpg';

bannerSource.pushData(bannerData);
```

## 安装和配置

1. 在项目的`oh-package.json5`中添加依赖：

```json
{
  "dependencies": {
    "@ohos/commonbusiness": "file:../Features/CommonBusiness",
    "@ohos/common": "file:../Common"
  }
}
```

2. 在需要使用的模块中导入：

```typescript
import { 
  BannerCard, 
  BaseHomeView, 
  CardData, 
  FullScreenNavigation 
} from '@ohos/commonbusiness';
```

## 注意事项

1. **依赖关系**：该模块依赖于Common基础模块，需要先安装Common模块
2. **响应式设计**：所有组件都支持多断点响应式布局
3. **组件复用**：BannerItem等组件支持复用优化，提高性能
4. **状态管理**：使用@Observed和@State进行状态管理
5. **类型安全**：提供完整的TypeScript类型定义
6. **扩展性**：视图模型支持泛型扩展，便于业务定制

## 版本信息

- 版本：1.0.0
- 许可证：Apache-2.0
- 主入口：Index.ets
- 依赖：@ohos/common

该模块为HarmonyOS应用的业务层开发提供了完整的组件库支持，通过可复用的业务组件和视图模型，大大提高了开发效率和代码质量。
