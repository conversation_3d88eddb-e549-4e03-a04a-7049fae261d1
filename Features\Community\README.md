# Community 社区探索模块

## 模块概述

Community模块是HarmonyOS应用的社区探索功能模块，专门为应用提供社区内容展示、文章浏览、开发者动态等功能。该模块基于Common基础模块和CommonBusiness业务模块构建，提供了丰富的社区内容展示组件，包括动态卡片、体验分享、开发者内容等，支持响应式设计和多种内容类型的展示。

## 模块结构

```
Features/Community/
├── Index.ets                 # 模块导出入口文件
├── src/main/ets/
│   ├── common/               # 通用常量
│   │   └── DiscoveryConstant.ets    # 发现页面常量
│   ├── component/            # 社区UI组件
│   │   ├── ArticleWebComponent.ets  # 文章Web组件
│   │   ├── DeveloperCard.ets        # 开发者卡片组件
│   │   ├── DeveloperItem.ets        # 开发者项组件
│   │   ├── ExperienceCard.ets       # 体验卡片组件
│   │   ├── ExperienceItem.ets       # 体验项组件
│   │   ├── FeedCard.ets             # 动态卡片组件
│   │   └── FeedItem.ets             # 动态项组件
│   ├── model/                # 数据模型
│   │   ├── DiscoverData.ets         # 发现数据模型
│   │   └── DiscoverModel.ets        # 发现业务模型
│   ├── service/              # 服务层
│   │   └── DiscoverService.ets      # 发现服务
│   ├── view/                 # 视图页面
│   │   ├── ArticleDetailView.ets    # 文章详情视图
│   │   ├── BannerDetailView.ets     # 横幅详情视图
│   │   └── ExplorationView.ets      # 探索主视图
│   └── viewmodel/            # 视图模型
│       ├── ArticleDetailViewModel.ets   # 文章详情视图模型
│       ├── ExplorationDetailState.ets   # 探索详情状态
│       ├── ExplorationState.ets         # 探索状态
│       └── ExplorationViewModel.ets     # 探索视图模型
```

## 核心功能模块

### 1. 探索主视图 (ExplorationView)

社区探索的主要视图组件，展示各种类型的社区内容。

```typescript
import { ExplorationView } from '@ohos/community';

@Component
struct CommunityPage {
  build() {
    Column() {
      ExplorationView()
    }
  }
}
```

### 2. 数据模型系统

#### DiscoverData - 发现数据模型

定义社区内容的数据结构。

```typescript
import { 
  DiscoverData, 
  DiscoverCardData, 
  DiscoverContent, 
  ArticleTypeEnum 
} from '@ohos/community';

// 创建发现内容
const discoverContent = new DiscoverContent();
discoverContent.id = 1;
discoverContent.type = ArticleTypeEnum.FEED;
discoverContent.title = '文章标题';
discoverContent.subTitle = '文章副标题';
discoverContent.desc = '文章描述';
discoverContent.author = '作者名称';
discoverContent.publishTime = '2024-01-01';
discoverContent.mediaUrl = 'article_image.jpg';
discoverContent.detailsUrl = 'https://example.com/article/1';

// 创建发现卡片数据
const discoverCardData = new DiscoverCardData();
discoverCardData.id = 1;
discoverCardData.name = '动态';
discoverCardData.type = ArticleTypeEnum.FEED;
discoverCardData.contents = [discoverContent];

// 创建发现数据
const discoverData = new DiscoverData();
discoverData.discoveryData = [discoverCardData];
```

#### 文章类型枚举

```typescript
import { ArticleTypeEnum } from '@ohos/community';

// 支持的文章类型
ArticleTypeEnum.FEED         // 动态类型
ArticleTypeEnum.EXPERIENCES  // 体验类型
ArticleTypeEnum.ARCHITECTURE // 架构类型
ArticleTypeEnum.DEVELOPER    // 开发者类型
ArticleTypeEnum.UNKNOWN      // 未知类型
```

### 3. 社区内容卡片组件

#### FeedCard - 动态卡片组件

展示社区动态内容的卡片组件。

```typescript
import { FeedCard, DiscoverContent } from '@ohos/community';

@Component
struct FeedSection {
  @State feedContents: DiscoverContent[] = [];

  aboutToAppear() {
    this.loadFeedData();
  }

  build() {
    Column() {
      FeedCard({
        discoverContents: this.feedContents,
        handleItemClick: (content: DiscoverContent) => {
          // 处理动态项点击事件
          console.log('Feed clicked:', content.title);
          this.navigateToDetail(content);
        }
      })
    }
  }

  private loadFeedData() {
    // 加载动态数据逻辑
  }

  private navigateToDetail(content: DiscoverContent) {
    // 跳转到详情页面
  }
}
```

#### ExperienceCard - 体验卡片组件

展示用户体验分享内容的卡片组件。

```typescript
import { ExperienceCard, DiscoverContent } from '@ohos/community';

@Component
struct ExperienceSection {
  @State experienceContents: DiscoverContent[] = [];

  build() {
    Column() {
      ExperienceCard({
        discoverContents: this.experienceContents,
        handleItemClick: (content: DiscoverContent) => {
          // 处理体验项点击事件
          this.showExperienceDetail(content);
        }
      })
    }
  }

  private showExperienceDetail(content: DiscoverContent) {
    // 显示体验详情
  }
}
```

#### DeveloperCard - 开发者卡片组件

展示开发者相关内容的卡片组件。

```typescript
import { DeveloperCard, DiscoverContent } from '@ohos/community';

@Component
struct DeveloperSection {
  @State developerContents: DiscoverContent[] = [];

  build() {
    Column() {
      DeveloperCard({
        discoverContents: this.developerContents,
        handleItemClick: (content: DiscoverContent) => {
          // 处理开发者内容点击事件
          this.openDeveloperProfile(content);
        }
      })
    }
  }

  private openDeveloperProfile(content: DiscoverContent) {
    // 打开开发者资料页面
  }
}
```

### 4. 业务模型和服务

#### DiscoverModel - 发现业务模型

管理社区发现页面的业务逻辑。

```typescript
import { DiscoverModel, DiscoverData } from '@ohos/community';

class CommunityManager {
  private discoverModel: DiscoverModel = DiscoverModel.getInstance();

  // 获取发现页面数据
  async getDiscoveryData(): Promise<DiscoverData> {
    try {
      const data = await this.discoverModel.getDiscoveryPage();
      return data;
    } catch (error) {
      console.error('Failed to get discovery data:', error);
      throw error;
    }
  }

  // 预加载发现数据
  async preloadData(): Promise<void> {
    try {
      await this.discoverModel.preloadDiscoveryData();
      console.log('Discovery data preloaded successfully');
    } catch (error) {
      console.error('Failed to preload discovery data:', error);
    }
  }
}
```

### 5. 视图模型系统

#### ExplorationViewModel - 探索视图模型

管理探索页面的状态和业务逻辑。

```typescript
import { 
  ExplorationViewModel, 
  ExplorationState, 
  ExplorationEventType 
} from '@ohos/community';

class CustomExplorationViewModel extends ExplorationViewModel {
  constructor() {
    super();
  }

  // 自定义事件处理
  handleCustomEvent(eventType: ExplorationEventType, param?: any) {
    switch (eventType) {
      case ExplorationEventType.LOAD_DISCOVERY_PAGE:
        this.loadDiscoveryPage();
        break;
      case ExplorationEventType.JUMP_DETAIL_DETAIL:
        this.jumpDetailView(param);
        break;
    }
  }

  // 获取当前状态
  getCurrentState(): ExplorationState {
    return this.getState();
  }
}
```

#### ExplorationState - 探索状态

管理探索页面的状态数据。

```typescript
import { ExplorationState, DiscoverCardData } from '@ohos/community';

@Component
struct ExplorationPage {
  @State explorationState: ExplorationState = new ExplorationState();

  aboutToAppear() {
    this.initializeState();
  }

  build() {
    Column() {
      // 根据状态渲染UI
      if (this.explorationState.discoveryData.length > 0) {
        this.renderDiscoveryContent()
      } else {
        this.renderEmptyState()
      }
    }
  }

  @Builder
  renderDiscoveryContent() {
    // 渲染发现内容
    ForEach(this.explorationState.discoveryData, (cardData: DiscoverCardData) => {
      // 根据卡片类型渲染不同组件
    })
  }

  @Builder
  renderEmptyState() {
    // 渲染空状态
    Text('暂无内容')
  }

  private initializeState() {
    // 初始化状态
  }
}
```

## 服务层架构

### DiscoverService - 发现服务

提供数据获取和缓存功能。

```typescript
import { DiscoverService, DiscoverData } from '@ohos/community';

class DataManager {
  private discoverService: DiscoverService = new DiscoverService();

  // 获取发现页面数据
  async getDiscoverPageData(): Promise<DiscoverData> {
    return await this.discoverService.getDiscoverPage();
  }

  // 从偏好设置获取数据
  async getDiscoverPageFromCache(): Promise<DiscoverData> {
    return await this.discoverService.getDiscoveryPageByPreference();
  }

  // 设置数据到偏好设置
  async cacheDiscoverPageData(data: DiscoverData): Promise<void> {
    await this.discoverService.setDiscoveryPageToPreference(data);
  }
}
```

## 响应式设计支持

所有组件都支持多断点响应式布局：

```typescript
// 组件会根据当前断点自动调整布局
// SM: 小屏幕 - 垂直滚动，单列显示
// MD: 中屏幕 - 水平滚动，多列显示
// LG: 大屏幕 - 网格布局，更多列显示
// XL: 超大屏幕 - 悬停效果，放大显示
```

## 内容类型处理

### 媒体类型支持

```typescript
import { MediaTypeEnum } from '@ohos/commonbusiness';

// 支持的媒体类型
MediaTypeEnum.IMAGE  // 图片类型
MediaTypeEnum.VIDEO  // 视频类型
```

### 内容分类

```typescript
// 根据文章类型显示不同的卡片组件
switch (cardData.type) {
  case ArticleTypeEnum.FEED:
    // 显示动态卡片
    FeedCard({ discoverContents: cardData.contents })
    break;
  case ArticleTypeEnum.EXPERIENCES:
    // 显示体验卡片
    ExperienceCard({ discoverContents: cardData.contents })
    break;
  case ArticleTypeEnum.DEVELOPER:
    // 显示开发者卡片
    DeveloperCard({ discoverContents: cardData.contents })
    break;
}
```

## 安装和配置

1. 在项目的`oh-package.json5`中添加依赖：

```json
{
  "dependencies": {
    "@ohos/community": "file:../Features/Community",
    "@ohos/commonbusiness": "file:../Features/CommonBusiness",
    "@ohos/common": "file:../Common"
  }
}
```

2. 在需要使用的模块中导入：

```typescript
import { 
  ExplorationView, 
  DiscoverModel, 
  FeedCard, 
  ExperienceCard, 
  DeveloperCard 
} from '@ohos/community';
```

## 注意事项

1. **依赖关系**：该模块依赖于Common基础模块和CommonBusiness业务模块
2. **数据缓存**：支持本地偏好设置缓存，提高加载性能
3. **响应式设计**：所有组件都支持多断点响应式布局
4. **组件复用**：使用Repeat组件和复用机制优化性能
5. **状态管理**：使用@Observed和@State进行状态管理
6. **网络处理**：支持网络数据获取和本地缓存回退机制
7. **类型安全**：提供完整的TypeScript类型定义

## 版本信息

- 版本：1.0.0
- 许可证：Apache-2.0
- 主入口：Index.ets
- 依赖：@ohos/common, @ohos/commonbusiness

该模块为HarmonyOS应用提供了完整的社区探索功能，通过丰富的内容展示组件和灵活的数据管理机制，为用户提供优质的社区内容浏览体验。
