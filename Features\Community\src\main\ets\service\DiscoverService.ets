// 导入基础服务工具包中的业务错误类型
import type { BusinessError } from '@kit.BasicServicesKit';
// 导入通用模块中的模拟请求和偏好设置管理器
import { MockRequest, PreferenceManager } from '@ohos/common';
// 导入发现数据类型
import type { DiscoverData } from '../model/DiscoverData';

// 导出发现服务类
export class DiscoverService {
  // 定义构造函数
  constructor() {
  }

  // 定义通过模拟获取示例列表的公共方法
  public getSampleListByMock(): Promise<DiscoverData> {
    // 返回Promise对象
    return new Promise((resolve: (value: DiscoverData) => void,
      reject: (reason?: Object) => void) => {
      // 调用模拟请求
      MockRequest.call<DiscoverData>(DiscoverTrigger.COMMUNITY_PAGE)
        // 处理成功响应
        .then((result: Object) => {
          // 解析结果
          resolve(result as DiscoverData);
        })
        // 处理错误响应
        .catch((error: BusinessError) => {
          // 拒绝错误
          reject(error);
        });
    });
  }

  // 定义获取发现页面的方法
  getDiscoverPage(): Promise<DiscoverData> {
    // 调用通过模拟获取示例列表的方法
    return this.getSampleListByMock();
  }

  // 定义通过偏好设置获取发现页面的方法
  getDiscoveryPageByPreference(): Promise<DiscoverData> {
    // 返回Promise对象
    return new Promise((resolve: (value: DiscoverData) => void,
      reject: (reason?: string) => void) => {
      // 获取偏好设置管理器实例
      PreferenceManager.getInstance()
        // 获取值
        .getValue<Record<string, DiscoverData>>(DiscoverTrigger.COMMUNITY_PAGE)
        // 处理响应
        .then((resp) => {
          // 如果响应为空
          if (!resp) {
            // 拒绝并提示偏好设置中没有数据
            reject('There is no data in the Preference');
          }
          // 转换响应类型
          resp = (resp as Record<string, DiscoverData>);
          // 获取返回值
          const ret = resp[DiscoverTrigger.COMMUNITY_PAGE];
          // 如果返回值为空
          if (!ret) {
            // 拒绝并提示偏好设置中没有数据
            reject('There is no data in the Preference');
          }
          // 解析返回值
          resolve(ret);
        });
    });
  }

  // 定义将发现页面设置到偏好设置的方法
  setDiscoveryPageToPreference(data: DiscoverData): Promise<void> {
    // 返回Promise对象
    return new Promise((resolve: () => void) => {
      // 检查偏好设置管理器是否有值
      PreferenceManager.getInstance().hasValue(DiscoverTrigger.COMMUNITY_PAGE)
        // 处理结果
        .then((result) => {
          // 如果有结果
          if (result) {
            // 获取偏好设置管理器实例
            PreferenceManager.getInstance()
              // 获取值
              .getValue<Record<string, DiscoverData>>(DiscoverTrigger.COMMUNITY_PAGE)
              // 处理响应
              .then((resp) => {
                // 转换响应类型
                resp = (resp as Record<string, DiscoverData>);
                // 设置响应数据
                resp[DiscoverTrigger.COMMUNITY_PAGE] = data;
                // 设置值到偏好设置管理器
                PreferenceManager.getInstance().setValue(DiscoverTrigger.COMMUNITY_PAGE, resp);
                // 解析完成
                resolve();
              });
          } else {
            // 创建记录对象
            const record: Record<string, DiscoverData> = {};
            // 设置记录数据
            record[DiscoverTrigger.COMMUNITY_PAGE] = data;
            // 设置值到偏好设置管理器
            PreferenceManager.getInstance().setValue(DiscoverTrigger.COMMUNITY_PAGE, record);
          }
        });
    });
  }
}

// 定义发现触发器枚举
enum DiscoverTrigger {
  // 发现页面
  COMMUNITY_PAGE = 'CommunityPage',
  // 发现文章
  COMMUNITY_ARTICLE = 'CommunityArticle',
}