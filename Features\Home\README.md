# Home 组件库展示模块

## 模块概述

Home模块是HarmonyOS应用的核心组件库展示模块，专门为开发者提供丰富的UI组件演示、代码预览和交互式学习功能。该模块基于Common基础模块和CommonBusiness业务模块构建，提供了完整的组件库浏览体验，包括组件列表展示、详细属性配置、实时代码预览、属性修改器等功能，是开发者学习和使用HarmonyOS UI组件的重要工具。

## 模块结构

```
Features/Home/
├── Index.ets                 # 模块导出入口文件
├── src/main/ets/
│   ├── component/            # 通用UI组件
│   │   ├── AttributeChangeArea.ets      # 属性变更区域组件
│   │   ├── CodeLabCard.ets              # 代码实验室卡片组件
│   │   ├── CodePreviewComponent.ets     # 代码预览组件
│   │   ├── ColorPickerComponent.ets     # 颜色选择器组件
│   │   ├── ComponentItem.ets            # 组件项组件
│   │   ├── DetailContentView.ets        # 详情内容视图组件
│   │   ├── ListCard.ets                 # 列表卡片组件
│   │   ├── MenuItemBuilder.ets          # 菜单项构建器
│   │   ├── OpacityComponent.ets         # 透明度组件
│   │   ├── PictureListCard.ets          # 图片列表卡片组件
│   │   ├── RecommendListItem.ets        # 推荐列表项组件
│   │   ├── SelectComponent.ets          # 选择组件
│   │   ├── SliderComponent.ets          # 滑块组件
│   │   ├── ToggleButtonComponent.ets    # 切换按钮组件
│   │   └── ToggleComponent.ets          # 切换组件
│   ├── componentdetailview/ # 组件详情视图集合
│   │   ├── ComponentDetailConfig.ets    # 组件详情配置
│   │   ├── buttonview/                  # 按钮组件详情
│   │   ├── textview/                    # 文本组件详情
│   │   ├── listview/                    # 列表组件详情
│   │   ├── gridview/                    # 网格组件详情
│   │   ├── swiperView/                  # 轮播组件详情
│   │   ├── tabview/                     # 标签页组件详情
│   │   ├── popup/                       # 弹窗组件详情
│   │   ├── picker/                      # 选择器组件详情
│   │   ├── progress/                    # 进度条组件详情
│   │   ├── rating/                      # 评分组件详情
│   │   ├── customdialog/                # 自定义对话框详情
│   │   ├── alertdialogview/             # 警告对话框详情
│   │   ├── actionsheetview/             # 操作表详情
│   │   ├── flex/                        # 弹性布局详情
│   │   ├── columnview/                  # 列布局详情
│   │   ├── rowview/                     # 行布局详情
│   │   ├── stackview/                   # 堆叠布局详情
│   │   ├── waterflow/                   # 瀑布流详情
│   │   ├── textinput/                   # 文本输入详情
│   │   ├── textarea/                    # 文本区域详情
│   │   ├── toggleview/                  # 切换视图详情
│   │   ├── Image/                       # 图片组件详情
│   │   ├── photopicker/                 # 图片选择器详情
│   │   ├── documentviewpicker/          # 文档选择器详情
│   │   ├── applinking/                  # 应用链接详情
│   │   ├── aicaption/                   # AI字幕详情
│   │   ├── imageaianalyzer/             # 图片AI分析详情
│   │   ├── texttospeech/                # 文本转语音详情
│   │   ├── penkit/                      # 手写笔工具详情
│   │   └── styletext/                   # 样式文本详情
│   ├── constant/             # 常量定义
│   │   └── DetailPageConstant.ets       # 详情页面常量
│   ├── model/                # 数据模型
│   │   ├── ComponentData.ets            # 组件数据模型
│   │   ├── ComponentDetailData.ets      # 组件详情数据模型
│   │   ├── ComponentDetailModel.ets     # 组件详情业务模型
│   │   └── ComponentListModel.ets       # 组件列表业务模型
│   ├── service/              # 服务层
│   │   └── ComponentLibraryService.ets  # 组件库服务
│   ├── util/                 # 工具类
│   │   ├── CodePreviewJSUtil.ets        # 代码预览JS工具
│   │   └── StringUtil.ets               # 字符串工具
│   ├── view/                 # 主要视图
│   │   ├── CodePreview.ets              # 代码预览视图
│   │   ├── ComponentDetailView.ets      # 组件详情视图
│   │   └── ComponentListView.ets        # 组件列表视图
│   └── viewmodel/            # 视图模型
│       ├── Attribute.ets                # 属性模型
│       ├── AttributeTypeEnum.ets        # 属性类型枚举
│       ├── CodePreviewPageVM.ets        # 代码预览页面视图模型
│       ├── CodePreviewState.ets         # 代码预览状态
│       ├── CommonAttributeFilter.ets    # 通用属性过滤器
│       ├── CommonAttributeModifier.ets  # 通用属性修改器
│       ├── CommonCodeGenerator.ets      # 通用代码生成器
│       ├── CommonDescriptor.ets         # 通用描述器
│       ├── ComponentCardSource.ets      # 组件卡片数据源
│       ├── ComponentDetailManager.ets   # 组件详情管理器
│       ├── ComponentDetailPageVM.ets    # 组件详情页面视图模型
│       ├── ComponentDetailState.ets     # 组件详情状态
│       ├── ComponentListState.ets       # 组件列表状态
│       ├── ComponentListViewModel.ets   # 组件列表视图模型
│       └── DescriptorWrapper.ets        # 描述器包装类
```

## 核心功能模块

### 1. 组件列表视图 (ComponentListView)

主要的组件库浏览界面，展示所有可用的UI组件。

```typescript
import { ComponentListView } from '@ohos/home';

@Component
struct HomePage {
  build() {
    Column() {
      ComponentListView()
    }
  }
}
```

### 2. 组件详情视图 (ComponentDetailView)

展示单个组件的详细信息、属性配置和代码预览。

```typescript
import { ComponentDetailView } from '@ohos/home';

@Component
struct ComponentDetailPage {
  @Prop componentName: string = 'Button';
  @Prop componentId: number = 1;

  build() {
    ComponentDetailView({
      componentName: this.componentName,
      componentId: this.componentId
    })
  }
}
```

### 3. 数据模型系统

#### ComponentData - 组件数据模型

定义组件库的数据结构。

```typescript
import { 
  ComponentData, 
  ComponentCardData, 
  ComponentContent, 
  CardTypeEnum, 
  CardStyleTypeEnum 
} from '@ohos/home';

// 创建组件内容
const componentContent = new ComponentContent();
componentContent.id = 1;
componentContent.title = '按钮';
componentContent.subTitle = 'Button组件';
componentContent.desc = '用于触发操作的按钮组件';
componentContent.image = 'button_icon.png';

// 创建组件卡片数据
const componentCardData = new ComponentCardData();
componentCardData.id = 1;
componentCardData.cardTitle = '基础组件';
componentCardData.cardSubTitle = '常用的基础UI组件';
componentCardData.cardType = CardTypeEnum.COMPONENT;
componentCardData.cardStyleType = CardStyleTypeEnum.LIST;
componentCardData.cardContents = [componentContent];

// 创建组件数据
const componentData = new ComponentData();
componentData.cardData = [componentCardData];
```

#### 卡片类型和样式枚举

```typescript
import { CardTypeEnum, CardStyleTypeEnum } from '@ohos/home';

// 卡片类型
CardTypeEnum.COMPONENT     // 组件类型
CardTypeEnum.CODELAB      // 代码实验室类型
CardTypeEnum.UNKNOWN      // 未知类型

// 卡片样式类型
CardStyleTypeEnum.LIST                // 列表样式
CardStyleTypeEnum.PICTURE            // 图片样式
CardStyleTypeEnum.PICTURE_ABOVE_LIST // 图片在上列表样式
```

### 4. 组件卡片展示

#### ListCard - 列表卡片组件

以列表形式展示组件信息。

```typescript
import { ListCard, ComponentCardData, ComponentContent } from '@ohos/home';

@Component
struct ComponentListSection {
  @State componentCardData: ComponentCardData = new ComponentCardData();

  build() {
    Column() {
      ListCard({
        componentCardData: this.componentCardData,
        handleItemClick: (componentContent: ComponentContent) => {
          // 处理组件项点击事件
          console.log('Component clicked:', componentContent.title);
          this.navigateToDetail(componentContent);
        }
      })
    }
  }

  private navigateToDetail(componentContent: ComponentContent) {
    // 跳转到组件详情页面
  }
}
```

#### PictureListCard - 图片列表卡片组件

以图片加列表的形式展示组件信息。

```typescript
import { PictureListCard, ComponentCardData } from '@ohos/home';

@Component
struct PictureComponentSection {
  @State componentCardData: ComponentCardData = new ComponentCardData();

  build() {
    Column() {
      PictureListCard({
        componentCardData: this.componentCardData,
        handleItemClick: (componentContent: ComponentContent) => {
          // 处理图片组件项点击事件
          this.showComponentDetail(componentContent);
        }
      })
    }
  }

  private showComponentDetail(componentContent: ComponentContent) {
    // 显示组件详情
  }
}
```

#### CodeLabCard - 代码实验室卡片组件

展示代码实验室相关内容。

```typescript
import { CodeLabCard, ComponentCardData } from '@ohos/home';

@Component
struct CodeLabSection {
  @State componentCardData: ComponentCardData = new ComponentCardData();

  build() {
    Column() {
      CodeLabCard({
        componentCardData: this.componentCardData
      })
        .onClick(() => {
          // 处理代码实验室卡片点击事件
          this.openCodeLab();
        })
    }
  }

  private openCodeLab() {
    // 打开代码实验室
  }
}
```

### 5. 代码预览系统

#### CodePreviewComponent - 代码预览组件

提供实时代码预览和编辑功能。

```typescript
import { CodePreviewComponent } from '@ohos/home';

@Component
struct CodePreviewSection {
  @State webNodeController: WebNodeController = new WebNodeController();
  @State code: string = `
@Component
struct ButtonExample {
  build() {
    Button('点击我')
      .width(120)
      .height(40)
      .backgroundColor(Color.Blue)
  }
}
  `;

  build() {
    Column() {
      CodePreviewComponent({
        webNodeController: this.webNodeController,
        code: this.code,
        componentName: 'Button',
        pageContainer: false,
        pushPage: () => {
          // 跳转到全屏代码预览页面
          this.openFullScreenPreview();
        }
      })
    }
  }

  private openFullScreenPreview() {
    // 打开全屏代码预览
  }
}
```

#### CodePreview - 代码预览视图

全屏代码预览页面。

```typescript
import { CodePreview } from '@ohos/home';

@Component
struct CodePreviewPage {
  build() {
    CodePreview()
  }
}
```

### 6. 属性配置系统

#### AttributeChangeArea - 属性变更区域

提供组件属性的动态配置功能。

```typescript
import { 
  AttributeChangeArea, 
  Attribute, 
  AttributeTypeEnum 
} from '@ohos/home';

@Component
struct AttributeConfigSection {
  @State attributes: Attribute[] = [
    {
      name: 'width',
      type: AttributeTypeEnum.NUMBER,
      value: 120,
      min: 50,
      max: 300
    },
    {
      name: 'backgroundColor',
      type: AttributeTypeEnum.COLOR,
      value: '#007DFF'
    }
  ];

  build() {
    Column() {
      AttributeChangeArea({
        attributes: this.attributes,
        componentName: 'Button'
      })
    }
  }
}
```

### 7. 业务模型和服务

#### ComponentListModel - 组件列表业务模型

管理组件列表的业务逻辑。

```typescript
import { ComponentListModel, ComponentData } from '@ohos/home';

class ComponentManager {
  private componentListModel: ComponentListModel = ComponentListModel.getInstance();

  // 获取组件列表数据
  async getComponentListData(): Promise<ComponentData> {
    try {
      const data = await this.componentListModel.getComponentPage();
      return data;
    } catch (error) {
      console.error('Failed to get component list data:', error);
      throw error;
    }
  }

  // 预加载组件数据
  async preloadComponentData(): Promise<void> {
    try {
      await this.componentListModel.preloadComponentData();
      console.log('Component data preloaded successfully');
    } catch (error) {
      console.error('Failed to preload component data:', error);
    }
  }
}
```

#### ComponentDetailModel - 组件详情业务模型

管理单个组件详情的业务逻辑。

```typescript
import { ComponentDetailModel, ComponentDetailData } from '@ohos/home';

class ComponentDetailManager {
  private componentDetailModel: ComponentDetailModel = new ComponentDetailModel();

  // 获取组件详情数据
  async getComponentDetailData(componentId: number): Promise<ComponentDetailData> {
    try {
      const data = await this.componentDetailModel.getComponentDetail(componentId);
      return data;
    } catch (error) {
      console.error('Failed to get component detail data:', error);
      throw error;
    }
  }

  // 更新组件属性
  updateComponentAttribute(attributeName: string, value: any): void {
    this.componentDetailModel.updateAttribute(attributeName, value);
  }
}
```

### 8. 视图模型系统

#### ComponentListViewModel - 组件列表视图模型

管理组件列表页面的状态和业务逻辑。

```typescript
import { 
  ComponentListViewModel, 
  ComponentListState, 
  ComponentListEventType 
} from '@ohos/home';

class CustomComponentListViewModel extends ComponentListViewModel {
  constructor() {
    super();
  }

  // 自定义事件处理
  handleCustomEvent(eventType: ComponentListEventType, param?: any) {
    switch (eventType) {
      case ComponentListEventType.LOAD_COMPONENT_PAGE:
        this.loadComponentPage();
        break;
      case ComponentListEventType.JUMP_DETAIL_DETAIL:
        this.jumpComponentDetail(param);
        break;
      case ComponentListEventType.UPDATE_FLOW_SECTION:
        this.updateFlowSection();
        break;
    }
  }

  // 获取当前状态
  getCurrentState(): ComponentListState {
    return this.getState();
  }
}
```

#### ComponentDetailPageVM - 组件详情页面视图模型

管理组件详情页面的状态和业务逻辑。

```typescript
import { 
  ComponentDetailPageVM, 
  ComponentDetailState, 
  InitComponentEvent, 
  ComponentDetailEvent 
} from '@ohos/home';

class CustomComponentDetailPageVM extends ComponentDetailPageVM {
  constructor() {
    super();
  }

  // 初始化组件
  async initializeComponent(componentId: number): Promise<void> {
    try {
      await this.sendEvent(new InitComponentEvent(componentId));
      const state = this.getState();
      console.log('Component initialized:', state);
    } catch (error) {
      console.error('Failed to initialize component:', error);
    }
  }

  // 更新组件详情
  async updateComponentDetail(eventData: any): Promise<void> {
    try {
      await this.sendEvent(new ComponentDetailEvent(eventData));
      const state = this.getState();
      console.log('Component detail updated:', state);
    } catch (error) {
      console.error('Failed to update component detail:', error);
    }
  }
}
```

## 组件详情视图配置

### ComponentDetailConfig - 组件详情配置

统一管理所有组件的详情配置。

```typescript
import { ComponentDetailConfig } from '@ohos/home';

// 获取按钮组件配置
const buttonConfig = ComponentDetailConfig.getButtonConfig();

// 获取文本组件配置
const textConfig = ComponentDetailConfig.getTextConfig();

// 获取列表组件配置
const listConfig = ComponentDetailConfig.getListConfig();

// 获取所有组件配置
const allConfigs = ComponentDetailConfig.getAllConfigs();
```

## 工具类和实用功能

### CodePreviewJSUtil - 代码预览JS工具

提供代码预览相关的JavaScript交互功能。

```typescript
import { CodePreviewJSUtil } from '@ohos/home';

// 运行JavaScript代码
CodePreviewJSUtil.codeViewRunJS('methodName()', 'params', () => {
  console.log('JavaScript executed successfully');
});

// 代码转HTML
CodePreviewJSUtil.codeToHtml(code, colorMode);
```

### StringUtil - 字符串工具

提供字符串处理相关的实用功能。

```typescript
import { StringUtil } from '@ohos/home';

// 格式化字符串
const formattedString = StringUtil.format('Hello {0}', 'World');

// 检查字符串是否为空
const isEmpty = StringUtil.isEmpty(str);

// 首字母大写
const capitalized = StringUtil.capitalize(str);
```

## 响应式设计支持

所有组件都支持多断点响应式布局：

```typescript
// 组件会根据当前断点自动调整布局
// SM: 小屏幕 - 垂直滚动，单列显示
// MD: 中屏幕 - 水平滚动，多列显示
// LG: 大屏幕 - 网格布局，更多列显示
// XL: 超大屏幕 - 分屏显示，详情页面并排显示
```

## 安装和配置

1. 在项目的`oh-package.json5`中添加依赖：

```json
{
  "dependencies": {
    "@ohos/home": "file:../Features/Home",
    "@ohos/commonbusiness": "file:../Features/CommonBusiness",
    "@ohos/common": "file:../Common"
  }
}
```

2. 在需要使用的模块中导入：

```typescript
import { 
  ComponentListView, 
  ComponentDetailView, 
  ComponentListModel, 
  CodePreviewComponent 
} from '@ohos/home';
```

## 注意事项

1. **依赖关系**：该模块依赖于Common基础模块和CommonBusiness业务模块
2. **Web组件**：代码预览功能需要Web组件支持，确保设备支持WebView
3. **响应式设计**：所有组件都支持多断点响应式布局
4. **组件复用**：使用复用机制优化大量组件的渲染性能
5. **状态管理**：使用@Observed和@ObjectLink进行复杂状态管理
6. **代码生成**：支持实时代码生成和预览功能
7. **属性配置**：提供丰富的属性配置和实时预览功能
8. **类型安全**：提供完整的TypeScript类型定义

## 版本信息

- 版本：1.0.0
- 许可证：Apache-2.0
- 主入口：Index.ets
- 依赖：@ohos/common, @ohos/commonbusiness

该模块为HarmonyOS应用提供了完整的组件库展示和学习功能，通过丰富的组件演示、实时代码预览和交互式属性配置，为开发者提供优质的组件学习和使用体验。
