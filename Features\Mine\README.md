# Mine 个人中心模块

## 模块概述

Mine模块是HarmonyOS应用的个人中心功能模块，专门为用户提供个人信息管理、应用设置、版本信息查看等功能。该模块基于Common基础模块构建，提供了简洁的个人中心界面，包括关于页面、版本检查、应用更新等功能，支持响应式设计和多种交互方式。

## 模块结构

```
Features/Mine/
├── Index.ets                 # 模块导出入口文件
├── src/main/ets/
│   ├── component/            # UI组件
│   │   ├── AboutItemCard.ets        # 关于页面项目卡片组件
│   │   └── CardItem.ets             # 卡片项目组件
│   ├── view/                 # 视图页面
│   │   ├── AboutView.ets            # 关于页面视图
│   │   └── MineView.ets             # 个人中心主视图
│   └── viewmodel/            # 视图模型
│       ├── AboutState.ets           # 关于页面状态
│       ├── AboutVM.ets              # 关于页面视图模型
│       ├── MinePageState.ets        # 个人中心页面状态
│       └── MinePageVM.ets           # 个人中心页面视图模型
```

## 核心功能模块

### 1. 个人中心主视图 (MineView)

个人中心的主要界面，展示用户相关的功能入口。

```typescript
import { MineView } from '@ohos/mine';

@Component
struct MinePage {
  build() {
    Column() {
      MineView()
    }
  }
}
```

### 2. 关于页面视图 (AboutView)

展示应用信息、版本信息和相关链接的页面。

```typescript
import { AboutView } from '@ohos/mine';

@Component
struct AboutPage {
  build() {
    Column() {
      AboutView()
    }
  }
}
```

### 3. 数据模型系统

#### MinePageState - 个人中心页面状态

管理个人中心页面的状态数据。

```typescript
import { MinePageState } from '@ohos/mine';

@Component
struct CustomMineView {
  @State minePageState: MinePageState = new MinePageState();

  aboutToAppear() {
    this.initializeState();
  }

  build() {
    Column() {
      // 根据状态渲染UI
      if (this.minePageState.aboutViewShow) {
        this.renderAboutView()
      }
      if (this.minePageState.feedbackViewShow) {
        this.renderFeedbackView()
      }
    }
  }

  @Builder
  renderAboutView() {
    // 渲染关于页面
    Text('关于页面')
  }

  @Builder
  renderFeedbackView() {
    // 渲染反馈页面
    Text('反馈页面')
  }

  private initializeState() {
    // 初始化状态
    this.minePageState.aboutViewShow = false;
    this.minePageState.feedbackViewShow = false;
  }
}
```

#### AboutState - 关于页面状态

管理关于页面的状态数据。

```typescript
import { AboutState } from '@ohos/mine';

@Component
struct CustomAboutView {
  @State aboutState: AboutState = new AboutState();

  aboutToAppear() {
    this.checkVersion();
  }

  build() {
    Column() {
      // 显示当前版本
      Text(`当前版本: ${this.aboutState.currentVersion}`)
        .fontSize(16)
        .margin({ bottom: 10 })

      // 显示更新状态
      if (this.aboutState.laterVersionExist) {
        Row() {
          Text('有新版本可用')
            .fontColor(Color.Red)
          if (this.aboutState.isLoadingUpdate) {
            LoadingProgress()
              .width(20)
              .height(20)
              .margin({ left: 10 })
          }
        }
      } else {
        Text('已是最新版本')
          .fontColor(Color.Green)
      }
    }
  }

  private checkVersion() {
    // 检查版本更新
    this.aboutState.currentVersion = '1.0.0';
    this.aboutState.laterVersionExist = false;
    this.aboutState.isLoadingUpdate = false;
  }
}
```

### 4. UI组件系统

#### CardItem - 卡片项目组件

通用的卡片项目组件，用于展示功能入口。

```typescript
import { CardItem } from '@ohos/mine';

@Component
struct FunctionList {
  @State showAbout: boolean = false;

  build() {
    Column() {
      CardItem({
        isShow: this.showAbout,
        textContent: $r('app.string.about'),
        symbolSrc: $r('sys.symbol.info_circle'),
        onclick: () => {
          // 处理点击事件
          this.showAbout = true;
          console.log('About clicked');
        },
        onClose: () => {
          // 处理关闭事件
          this.showAbout = false;
          console.log('About closed');
        }
      })

      CardItem({
        isShow: false,
        textContent: $r('app.string.settings'),
        symbolSrc: $r('sys.symbol.gear'),
        onclick: () => {
          // 处理设置点击事件
          this.openSettings();
        },
        onClose: () => {
          // 处理设置关闭事件
        }
      })
    }
  }

  private openSettings() {
    // 打开设置页面
    console.log('Opening settings');
  }
}
```

#### AboutItemCard - 关于页面项目卡片组件

专门用于关于页面的项目卡片组件。

```typescript
import { AboutItemCard } from '@ohos/mine';

@Component
struct AboutSection {
  build() {
    Column() {
      Text('应用信息')
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .margin({ bottom: 16 })

      AboutItemCard()
        .margin({ bottom: 12 })

      // 其他关于信息
      this.buildOtherInfo()
    }
  }

  @Builder
  buildOtherInfo() {
    Column() {
      Text('开发者信息')
        .fontSize(14)
        .fontColor(Color.Gray)
      Text('版权信息')
        .fontSize(14)
        .fontColor(Color.Gray)
        .margin({ top: 8 })
    }
  }
}
```

### 5. 视图模型系统

#### MinePageVM - 个人中心页面视图模型

管理个人中心页面的业务逻辑和状态。

```typescript
import { 
  MinePageVM, 
  AboutBindSheetEvent, 
  FeedbackBindSheetEvent 
} from '@ohos/mine';

class MinePageManager {
  private viewModel: MinePageVM = MinePageVM.getInstance();

  // 显示关于页面
  showAboutView(): void {
    this.viewModel.sendEvent(new AboutBindSheetEvent(true));
  }

  // 隐藏关于页面
  hideAboutView(): void {
    this.viewModel.sendEvent(new AboutBindSheetEvent(false));
  }

  // 显示反馈页面
  showFeedbackView(): void {
    this.viewModel.sendEvent(new FeedbackBindSheetEvent(true));
  }

  // 隐藏反馈页面
  hideFeedbackView(): void {
    this.viewModel.sendEvent(new FeedbackBindSheetEvent(false));
  }

  // 获取当前状态
  getCurrentState() {
    return this.viewModel.getState();
  }
}

// 使用示例
const mineManager = new MinePageManager();

// 显示关于页面
mineManager.showAboutView();

// 获取状态
const state = mineManager.getCurrentState();
console.log('About view show:', state.aboutViewShow);
console.log('Feedback view show:', state.feedbackViewShow);
```

#### AboutVM - 关于页面视图模型

管理关于页面的业务逻辑，包括版本检查和更新功能。

```typescript
import { 
  AboutVM, 
  CheckVersionEvent, 
  UpdateVersionEvent, 
  ViewRegistrationInfoEvent 
} from '@ohos/mine';

class AboutManager {
  private viewModel: AboutVM = AboutVM.getInstance();

  // 检查版本更新
  checkForUpdates(): void {
    this.viewModel.sendEvent(new CheckVersionEvent());
  }

  // 执行版本更新
  performUpdate(): void {
    this.viewModel.sendEvent(new UpdateVersionEvent());
  }

  // 查看注册信息
  viewRegistrationInfo(): void {
    this.viewModel.sendEvent(new ViewRegistrationInfoEvent());
  }

  // 获取当前状态
  getCurrentState() {
    return this.viewModel.getState();
  }

  // 监听状态变化
  onStateChange(callback: (state: any) => void): void {
    const state = this.getCurrentState();
    callback(state);
  }
}

// 使用示例
const aboutManager = new AboutManager();

// 检查更新
aboutManager.checkForUpdates();

// 监听状态变化
aboutManager.onStateChange((state) => {
  if (state.laterVersionExist) {
    console.log('New version available:', state.currentVersion);
    // 可以选择自动更新或提示用户
    aboutManager.performUpdate();
  } else {
    console.log('App is up to date');
  }
});
```

### 6. 事件系统

#### 关于页面事件

```typescript
import { 
  AboutBindSheetEvent, 
  CheckVersionEvent, 
  UpdateVersionEvent, 
  ViewRegistrationInfoEvent 
} from '@ohos/mine';

// 关于页面绑定弹窗事件
const showAboutEvent = new AboutBindSheetEvent(true);
const hideAboutEvent = new AboutBindSheetEvent(false);

// 版本检查事件
const checkVersionEvent = new CheckVersionEvent();

// 版本更新事件
const updateVersionEvent = new UpdateVersionEvent();

// 查看注册信息事件
const viewRegistrationEvent = new ViewRegistrationInfoEvent();
```

#### 反馈页面事件

```typescript
import { FeedbackBindSheetEvent } from '@ohos/mine';

// 反馈页面绑定弹窗事件
const showFeedbackEvent = new FeedbackBindSheetEvent(true);
const hideFeedbackEvent = new FeedbackBindSheetEvent(false);
```

### 7. 响应式设计支持

所有组件都支持多断点响应式布局：

```typescript
// 组件会根据当前断点自动调整布局
// SM: 小屏幕 - 底部弹窗显示
// MD: 中屏幕 - 居中弹窗显示
// LG: 大屏幕 - 居中弹窗显示
// XL: 超大屏幕 - 固定尺寸居中弹窗显示

// 弹窗类型自动适配
preferType: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.SM ? 
  SheetType.BOTTOM : SheetType.CENTER

// 弹窗尺寸自动适配
height: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ?
  ((this.globalInfoModel.deviceHeight - this.globalInfoModel.decorHeight) *
  CommonConstants.SHEET_HEIGHT_RATIO_XL) : SheetSize.LARGE
```

### 8. 版本管理功能

#### 版本检查

```typescript
// 自动检查版本更新
aboutToAppear(): void {
  this.viewModel.sendEvent(new CheckVersionEvent());
}

// 版本状态显示
if (this.aboutState.laterVersionExist) {
  Badge({
    value: '',
    style: { badgeSize: 6, badgeColor: $r('app.color.about_badge_color') },
    position: BadgePosition.Right,
  }) {
    Text($r('app.string.version_information'))
  }
}
```

#### 版本更新

```typescript
// 点击更新版本
.onClick(() => {
  if (this.aboutState.laterVersionExist) {
    this.viewModel.sendEvent(new UpdateVersionEvent());
  }
})
```

## 安装和配置

1. 在项目的`oh-package.json5`中添加依赖：

```json
{
  "dependencies": {
    "@ohos/mine": "file:../Features/Mine",
    "@ohos/common": "file:../Common"
  }
}
```

2. 在需要使用的模块中导入：

```typescript
import { 
  MineView, 
  AboutView, 
  CardItem, 
  AboutItemCard 
} from '@ohos/mine';
```

## 注意事项

1. **依赖关系**：该模块依赖于Common基础模块
2. **权限要求**：版本更新功能可能需要网络权限和安装权限
3. **响应式设计**：所有组件都支持多断点响应式布局
4. **状态管理**：使用@Observed和@State进行状态管理
5. **事件处理**：使用事件驱动的架构模式
6. **弹窗管理**：支持多种弹窗类型和自适应尺寸
7. **版本检查**：支持自动版本检查和更新提醒
8. **外部跳转**：支持跳转到外部浏览器查看注册信息

## 版本信息

- 版本：1.0.0
- 许可证：Apache-2.0
- 主入口：Index.ets
- 依赖：@ohos/common

该模块为HarmonyOS应用提供了完整的个人中心功能，通过简洁的界面设计和丰富的功能模块，为用户提供便捷的个人信息管理和应用设置体验。
