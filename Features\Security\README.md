# Security 安全实践模块

## 模块概述

Security模块是HarmonyOS应用的安全实践展示模块，专门为开发者提供安全编程实践、安全示例代码和安全最佳实践的学习平台。该模块基于Common基础模块和CommonBusiness业务模块构建，提供了丰富的安全实践示例展示，包括安全编程指南、代码示例、实践案例等功能，支持响应式设计和多种内容展示方式。

## 模块结构

```
Features/Security/
├── Index.ets                 # 模块导出入口文件
├── src/main/ets/
│   ├── common/               # 通用常量
│   │   └── SampleConstant.ets       # 示例常量定义
│   ├── component/            # UI组件
│   │   ├── BaseCategoryView.ets     # 基础分类视图组件
│   │   ├── CategorySamples.ets      # 分类示例组件
│   │   ├── PictureAboveTextCard.ets # 图片在上文本卡片组件
│   │   ├── PictureCard.ets          # 图片卡片组件
│   │   ├── SampleCard.ets           # 示例卡片组件
│   │   ├── SampleComponent.ets      # 示例组件
│   │   ├── SampleListCard.ets       # 示例列表卡片组件
│   │   ├── SampleScrollCard.ets     # 示例滚动卡片组件
│   │   └── TagLabel.ets             # 标签标签组件
│   ├── constant/             # 常量定义
│   │   └── CommonConstants.ets      # 通用常量
│   ├── model/                # 数据模型
│   │   ├── SampleData.ets           # 示例数据模型
│   │   ├── SampleDetailData.ets     # 示例详情数据模型
│   │   ├── SampleDetailModel.ets    # 示例详情业务模型
│   │   └── SampleModel.ets          # 示例业务模型
│   ├── service/              # 服务层
│   │   └── SampleService.ets        # 示例服务
│   ├── view/                 # 视图页面
│   │   ├── PracticeDetailView.ets   # 实践详情视图
│   │   └── PracticesView.ets        # 实践主视图
│   └── viewmodel/            # 视图模型
│       ├── PracticeState.ets        # 实践状态
│       ├── PracticeViewModel.ets    # 实践视图模型
│       ├── SampleDetailPageVM.ets   # 示例详情页面视图模型
│       └── SampleDetailState.ets    # 示例详情状态
```

## 核心功能模块

### 1. 实践主视图 (PracticesView)

安全实践的主要展示界面，展示各种安全实践分类和示例。

```typescript
import { PracticesView } from '@ohos/security';

@Component
struct SecurityPage {
  build() {
    Column() {
      PracticesView()
    }
  }
}
```

### 2. 实践详情视图 (PracticeDetailView)

展示单个安全实践示例的详细信息和代码。

```typescript
import { PracticeDetailView } from '@ohos/security';

@Component
struct PracticeDetailPage {
  build() {
    PracticeDetailView()
  }
}
```

### 3. 数据模型系统

#### SampleData - 示例数据模型

定义安全实践示例的数据结构。

```typescript
import { 
  SampleData, 
  SampleCategory, 
  SampleCardData, 
  SampleContent, 
  CardTypeEnum, 
  CardStyleTypeEnum, 
  MediaTypeEnum 
} from '@ohos/security';

// 创建示例内容
const sampleContent = new SampleContent();
sampleContent.id = 1;
sampleContent.type = CardTypeEnum.SAMPLE;
sampleContent.mediaType = MediaTypeEnum.IMAGE;
sampleContent.mediaUrl = 'security_example.png';
sampleContent.title = '数据加密实践';
sampleContent.subTitle = '如何正确加密敏感数据';
sampleContent.tags = ['加密', '安全', '数据保护'];

// 创建示例卡片数据
const sampleCardData = new SampleCardData();
sampleCardData.id = 1;
sampleCardData.cardTitle = '数据安全';
sampleCardData.cardSubTitle = '数据保护最佳实践';
sampleCardData.cardType = CardTypeEnum.SAMPLE;
sampleCardData.cardStyleType = CardStyleTypeEnum.LIST;
sampleCardData.sampleContents = [sampleContent];

// 创建示例分类
const sampleCategory = new SampleCategory();
sampleCategory.id = 1;
sampleCategory.categoryName = '数据安全';
sampleCategory.categoryType = 1;
sampleCategory.tabIcon = 'security_icon.png';
sampleCategory.sampleCards = [sampleCardData];

// 创建示例数据
const sampleData = new SampleData();
sampleData.sampleCategories = [sampleCategory];
```

#### SampleDetailData - 示例详情数据模型

定义单个安全实践示例的详细信息。

```typescript
import { 
  SingleSampleData, 
  SampleCardDetail, 
  SampleTypeEnum 
} from '@ohos/security';

// 创建单个示例数据
const singleSampleData = new SingleSampleData();
singleSampleData.id = 1;
singleSampleData.title = 'AES加密示例';
singleSampleData.desc = '演示如何使用AES算法加密敏感数据';
singleSampleData.preInstalled = false;
singleSampleData.sampleType = SampleTypeEnum.COMMON_SAMPLE;
singleSampleData.isFavorite = false;
singleSampleData.mediaType = 1;
singleSampleData.mediaUrl = 'aes_encryption_demo.mp4';
singleSampleData.originalUrl = 'https://example.com/aes-demo';
singleSampleData.moduleName = 'SecurityModule';
singleSampleData.abilityName = 'EncryptionAbility';

// 创建示例卡片详情
const sampleCardDetail = new SampleCardDetail();
sampleCardDetail.id = 1;
sampleCardDetail.categoryType = 1;
sampleCardDetail.sampleDetail = [singleSampleData];
```

### 4. 示例展示组件

#### SampleCard - 示例卡片组件

展示单个安全实践示例的卡片组件。

```typescript
import { SampleCard, SampleCardData } from '@ohos/security';

@Component
struct SecuritySampleSection {
  @State sampleCard: SampleCardData = new SampleCardData();
  @State sampleIndex: number = 0;

  build() {
    Column() {
      SampleCard({
        sampleCard: this.sampleCard,
        sampleIndex: this.sampleIndex
      })
    }
  }
}
```

#### SampleScrollCard - 示例滚动卡片组件

以滚动方式展示多个安全实践示例。

```typescript
import { SampleScrollCard, SampleCardData } from '@ohos/security';

@Component
struct SecurityScrollSection {
  @State sampleCardData: SampleCardData = new SampleCardData();

  build() {
    Column() {
      SampleScrollCard({
        sampleCardData: this.sampleCardData,
        handleItemClick: (sampleContent: any) => {
          // 处理示例项点击事件
          console.log('Sample clicked:', sampleContent.title);
          this.navigateToDetail(sampleContent);
        }
      })
    }
  }

  private navigateToDetail(sampleContent: any) {
    // 跳转到示例详情页面
  }
}
```

#### SampleListCard - 示例列表卡片组件

以列表形式展示安全实践示例。

```typescript
import { SampleListCard, SampleCardData } from '@ohos/security';

@Component
struct SecurityListSection {
  @State sampleCardData: SampleCardData = new SampleCardData();

  build() {
    Column() {
      SampleListCard({
        sampleCardData: this.sampleCardData,
        handleItemClick: (sampleContent: any) => {
          // 处理列表项点击事件
          this.showSampleDetail(sampleContent);
        }
      })
    }
  }

  private showSampleDetail(sampleContent: any) {
    // 显示示例详情
  }
}
```

#### SampleComponent - 示例组件

展示单个安全实践示例的详细内容。

```typescript
import { SampleComponent, SampleDetailData } from '@ohos/security';

@Component
struct SecuritySampleDetail {
  @State singleSampleData: SampleDetailData = new SampleDetailData();
  @State sampleIndex: number = 0;
  @State showIndicator: boolean = true;

  build() {
    Column() {
      SampleComponent({
        singleSampleData: this.singleSampleData,
        sampleIndex: this.sampleIndex,
        showIndicator: this.showIndicator
      })
    }
  }
}
```

### 5. 分类展示组件

#### CategorySamples - 分类示例组件

按分类展示安全实践示例。

```typescript
import { CategorySamples, SampleCategory } from '@ohos/security';

@Component
struct SecurityCategorySection {
  @State sampleCategory: SampleCategory = new SampleCategory();

  build() {
    Column() {
      CategorySamples({
        sampleCategory: this.sampleCategory,
        handleItemClick: (sampleContent: any) => {
          // 处理分类示例点击事件
          this.openCategorySample(sampleContent);
        }
      })
    }
  }

  private openCategorySample(sampleContent: any) {
    // 打开分类示例
  }
}
```

#### BaseCategoryView - 基础分类视图组件

提供分类视图的基础功能。

```typescript
import { BaseCategoryView } from '@ohos/security';

@Component
struct SecurityCategoryView {
  build() {
    Column() {
      BaseCategoryView()
    }
  }
}
```

### 6. 业务模型和服务

#### SampleModel - 示例业务模型

管理安全实践示例的业务逻辑。

```typescript
import { SampleModel, SampleData } from '@ohos/security';

class SecuritySampleManager {
  private sampleModel: SampleModel = SampleModel.getInstance();

  // 获取示例页面数据
  async getSamplePageData(page: number, pageSize: number): Promise<SampleData> {
    try {
      const result = await this.sampleModel.getSamplePage(page, pageSize);
      return result.data;
    } catch (error) {
      console.error('Failed to get sample page data:', error);
      throw error;
    }
  }

  // 获取示例列表数据
  async getSampleListData(categoryType: number, page: number, pageSize: number): Promise<any[]> {
    try {
      const result = await this.sampleModel.getSampleList(categoryType, page, pageSize);
      return result.data;
    } catch (error) {
      console.error('Failed to get sample list data:', error);
      throw error;
    }
  }

  // 预加载示例页面数据
  async preloadSampleData(): Promise<void> {
    try {
      await this.sampleModel.preloadSamplePageData();
      console.log('Sample data preloaded successfully');
    } catch (error) {
      console.error('Failed to preload sample data:', error);
    }
  }
}
```

#### SampleDetailModel - 示例详情业务模型

管理单个安全实践示例的详细信息。

```typescript
import { SampleDetailModel, SingleSampleData } from '@ohos/security';

class SecuritySampleDetailManager {
  private sampleDetailModel: SampleDetailModel = SampleDetailModel.getInstance();

  // 获取示例卡片详情
  async getSampleCardDetails(sampleCardId: number): Promise<SingleSampleData[]> {
    try {
      const data = await this.sampleDetailModel.getSampleCardDetails(sampleCardId);
      return data;
    } catch (error) {
      console.error('Failed to get sample card details:', error);
      throw error;
    }
  }
}
```

### 7. 视图模型系统

#### PracticeViewModel - 实践视图模型

管理安全实践页面的状态和业务逻辑。

```typescript
import { 
  PracticeViewModel, 
  PracticeState, 
  PracticeEventType, 
  LoadSamplePageParam, 
  SampleDetailParams 
} from '@ohos/security';

class SecurityPracticeManager {
  private viewModel: PracticeViewModel = PracticeViewModel.getInstance();

  // 加载示例页面
  loadSamplePage(param: LoadSamplePageParam): void {
    this.viewModel.sendEvent({
      type: PracticeEventType.LOAD_SAMPLE_PAGE,
      param: param
    });
  }

  // 加载示例列表
  loadSampleList(categoryType: number): void {
    this.viewModel.sendEvent({
      type: PracticeEventType.LOAD_SAMPLE_LIST,
      param: { categoryType }
    });
  }

  // 跳转到详情页面
  jumpToDetail(params: SampleDetailParams): void {
    this.viewModel.sendEvent({
      type: PracticeEventType.JUMP_DETAIL_DETAIL,
      param: params
    });
  }

  // 获取当前状态
  getCurrentState(): PracticeState {
    return this.viewModel.getState();
  }
}
```

#### SampleDetailPageVM - 示例详情页面视图模型

管理示例详情页面的状态和业务逻辑。

```typescript
import { 
  SampleDetailPageVM, 
  SampleDetailState, 
  InitSampleDetailEvent, 
  SetIndexEvent, 
  PopEvent 
} from '@ohos/security';

class SecuritySampleDetailManager {
  private viewModel: SampleDetailPageVM = SampleDetailPageVM.getInstance();

  // 初始化示例详情
  initSampleDetail(sampleCardId: number): void {
    this.viewModel.sendEvent(new InitSampleDetailEvent(sampleCardId));
  }

  // 设置当前索引
  setCurrentIndex(index: number): void {
    this.viewModel.sendEvent(new SetIndexEvent(index));
  }

  // 返回上一页
  popPage(): void {
    this.viewModel.sendEvent(new PopEvent());
  }

  // 获取当前状态
  getCurrentState(): SampleDetailState {
    return this.viewModel.getState();
  }
}
```

### 8. 标签和分类功能

#### TagLabel - 标签标签组件

展示安全实践示例的标签信息。

```typescript
import { TagLabel } from '@ohos/security';

@Component
struct SecurityTagSection {
  @State tags: string[] = ['加密', '认证', '权限', '网络安全'];

  build() {
    Column() {
      ForEach(this.tags, (tag: string) => {
        TagLabel({
          tagText: tag,
          tagColor: Color.Blue,
          onTagClick: () => {
            // 处理标签点击事件
            this.filterByTag(tag);
          }
        })
      })
    }
  }

  private filterByTag(tag: string) {
    // 根据标签过滤内容
    console.log('Filter by tag:', tag);
  }
}
```

### 9. 响应式设计支持

所有组件都支持多断点响应式布局：

```typescript
// 组件会根据当前断点自动调整布局
// SM: 小屏幕 - 垂直滚动，单列显示
// MD: 中屏幕 - 水平滚动，多列显示
// LG: 大屏幕 - 网格布局，更多列显示
// XL: 超大屏幕 - 分屏显示，详情页面并排显示

// 网格列配置
GridCol({
  span: { sm: 4, md: 6, lg: 8 },
  offset: { sm: 0, md: 1, lg: 2 }
})

// 内边距配置
.padding(new BreakpointType({
  sm: $r('sys.float.padding_level8'),
  md: $r('sys.float.padding_level12'),
  lg: $r('sys.float.padding_level16')
}).getValue(this.globalInfoModel.currentBreakpoint))
```

### 10. 媒体内容支持

#### 图片和视频展示

```typescript
// 根据媒体类型显示不同内容
if (this.singleSampleData.mediaType === MediaTypeEnum.IMAGE) {
  Image($rawfile(this.singleSampleData.mediaUrl))
    .draggable(false)
    .alt($r('app.media.img_placeholder'))
    .objectFit(ImageFit.Contain)
} else {
  VideoComponent({
    mediaSrc: this.singleSampleData.mediaUrl,
    autoPlay: true,
    loopPlay: true,
    clickPause: false,
    startVisibleRatio: 0.5
  })
}
```

## 安装和配置

1. 在项目的`oh-package.json5`中添加依赖：

```json
{
  "dependencies": {
    "@ohos/security": "file:../Features/Security",
    "@ohos/commonbusiness": "file:../Features/CommonBusiness",
    "@ohos/common": "file:../Common"
  }
}
```

2. 在需要使用的模块中导入：

```typescript
import { 
  PracticesView, 
  SampleModel, 
  SingleSampleData, 
  SampleCard, 
  SampleComponent 
} from '@ohos/security';
```

## 注意事项

1. **依赖关系**：该模块依赖于Common基础模块和CommonBusiness业务模块
2. **媒体资源**：支持图片和视频两种媒体类型的展示
3. **响应式设计**：所有组件都支持多断点响应式布局
4. **数据缓存**：支持本地偏好设置缓存，提高加载性能
5. **状态管理**：使用@Observed和@ObjectLink进行复杂状态管理
6. **分页加载**：支持分页加载和无限滚动
7. **分类管理**：支持多级分类和标签过滤
8. **安全实践**：专注于安全编程实践和最佳实践展示

## 版本信息

- 版本：1.0.0
- 许可证：Apache-2.0
- 主入口：Index.ets
- 依赖：@ohos/common, @ohos/commonbusiness

该模块为HarmonyOS应用提供了完整的安全实践展示功能，通过丰富的示例展示、分类管理和交互式学习，为开发者提供优质的安全编程学习和实践体验。
