// 导入通用模块中的全局信息模型和页面上下文类型
import type { GlobalInfoModel, PageContext } from '@ohos/common';

// 导入通用业务模块中的相关组件和类型
import {
  BaseHomeView,
  FullScreenNavigation,
  LoadingMoreItemBuilder,
} from '@ohos/commonbusiness';

// 导入实践状态类型
import type { SecurityState } from '../viewmodel/SecurityState';
// 导入实践视图模型
import { SecurityViewModel } from '../viewmodel/SecurityViewModel';

// 使用Component装饰器定义实践视图组件，设置非活跃时冻结
@Component({ freezeWhenInactive: true })
export struct SecurityView {
  // 定义视图模型实例
  viewModel: SecurityViewModel = SecurityViewModel.getInstance();
  // 使用StorageProp装饰器获取全局信息模型
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel =
    AppStorage.get('GlobalInfoModel')!;
  // 使用State装饰器定义实践状态
  @State practiceState: SecurityState = this.viewModel.getState();
  // 定义列表滚动器私有变量
  private listScroller: Scroller = new Scroller();
  // 定义示例页面上下文私有变量
  private samplePageContext: PageContext = AppStorage.get('samplePageContext')!;

  // 定义即将出现生命周期方法
  aboutToAppear(): void {
    // 组件初始化
  }

  // 使用Builder装饰器定义内容视图构建器
  @Builder
  ContentViewBuilder() {
    // 创建列表组件
    List({ scroller: this.listScroller }) {
      // 创建列表项
      ListItem() {
        // 创建加载更多项目构建器
        LoadingMoreItemBuilder(this.practiceState.loadingModel)
      }
    }
    // 设置宽度
    .width('100%')
    // 设置高度
    .height('100%')
    // 设置背景颜色
    .backgroundColor($r('sys.color.background_secondary'))
  }
  // 使用Builder装饰器定义顶部标题视图构建器
  @Builder
  TopTitleViewBuilder() {
    // 创建全屏导航组件
    FullScreenNavigation({
      topNavigationData: this.practiceState.topNavigationData,
    })
  }

  // 定义构建方法
  build() {
    // 创建导航组件
    Navigation(this.samplePageContext.navPathStack) {
      // 创建基础主页视图
      BaseHomeView({
        loadingModel: this.practiceState.loadingModel,
        contentView: () => {
          // 调用内容视图构建器
          this.ContentViewBuilder()
        },
        topTitleView: () => {
          // 调用顶部标题视图构建器
          this.TopTitleViewBuilder()
        },
        reloadData: () => {
          // 重新加载数据
        },
      })
    }
    // 设置导航模式为堆栈
    .mode(NavigationMode.Stack)
    // 隐藏标题栏
    .hideTitleBar(true)
  }
}