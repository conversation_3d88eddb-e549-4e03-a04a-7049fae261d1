// 导入通用业务模块中的相关类和类型
import {
  BaseHomeEventParam,
  BaseHomeViewModel,
} from '@ohos/commonbusiness';
// 导入实践状态
import { SecurityState } from './SecurityState';

// 导出实践视图模型类
export class SecurityViewModel extends BaseHomeViewModel<SecurityState> {
  // 定义静态实例私有变量
  private static instance: SecurityViewModel;

  // 定义私有构造函数
  private constructor() {
    // 调用父类构造函数
    super(new SecurityState());
    // 设置顶部导航数据标题
    this.state.topNavigationData.title = $r('app.string.security_name');
  }

  // 定义获取实例的静态方法
  static getInstance(): SecurityViewModel {
    // 如果实例不存在
    if (!SecurityViewModel.instance) {
      // 创建新实例
      SecurityViewModel.instance = new SecurityViewModel();
    }
    // 返回实例
    return SecurityViewModel.instance;
  }

  // 定义发送事件的方法
  sendEvent<T>(eventParam: BaseHomeEventParam<T>): void | boolean {
    // 调用父类发送事件方法
    return super.sendEvent(eventParam);
  }

}