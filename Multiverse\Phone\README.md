# Phone 手机应用主模块

## 模块概述

Phone模块是HarmonyOS应用的主入口模块，作为整个应用的核心容器，负责应用的生命周期管理、页面导航、响应式布局和多模块集成。该模块集成了Home、Security、Community、Mine等功能模块，提供了完整的移动应用体验，支持手机、平板和2in1设备的多形态适配。

## 模块结构

```
Multiverse/Phone/
├── src/main/
│   ├── ets/                  # TypeScript源码目录
│   │   ├── component/        # UI组件
│   │   │   ├── CustomSideBar.ets    # 自定义侧边栏组件
│   │   │   └── CustomTabBar.ets     # 自定义标签栏组件
│   │   ├── entryability/     # 入口能力
│   │   │   └── EntryAbility.ets     # 应用主入口能力
│   │   ├── entrybackupability/ # 备份能力
│   │   │   └── EntryBackupAbility.ets # 应用备份恢复能力
│   │   ├── model/            # 数据模型
│   │   │   └── TabBarModel.ets      # 标签栏数据模型
│   │   ├── page/             # 页面
│   │   │   ├── MainPage.ets         # 主页面
│   │   │   └── SplashPage.ets       # 启动页面
│   │   ├── phoneformability/ # 表单能力
│   │   │   └── PhoneFormAbility.ets # 手机表单能力
│   │   ├── util/             # 工具类
│   │   │   └── ContextConfig.ts     # 上下文配置
│   │   ├── viewmodel/        # 视图模型
│   │   │   └── SplashViewModel.ets  # 启动页视图模型
│   │   └── widget/           # 小组件
│   │       └── pages/               # 小组件页面
│   ├── module.json5          # 模块配置文件
│   └── resources/            # 资源文件
│       ├── base/             # 基础资源
│       ├── dark/             # 深色主题资源
│       ├── en_US/            # 英文资源
│       ├── zh_CN/            # 中文资源
│       ├── rawfile/          # 原始文件
│       └── resfile/          # 资源文件
└── oh-package.json5          # 包配置文件
```

## 核心功能模块

### 1. 应用入口能力 (EntryAbility)

应用的主要入口点，负责应用生命周期管理和初始化。

```typescript
import EntryAbility from './entryability/EntryAbility';

// EntryAbility自动处理以下功能：
// - 应用启动和初始化
// - 窗口创建和配置
// - 全屏显示设置
// - 断点监听注册
// - 页面上下文创建
// - Web工具初始化
// - 状态栏配置
```

### 2. 主页面 (MainPage)

应用的主要界面，集成所有功能模块的导航容器。

```typescript
import { MainPageBuilder } from './page/MainPage';

@Component
struct AppContainer {
  build() {
    Column() {
      // 使用主页面构建器
      MainPageBuilder()
    }
  }
}
```

#### 主页面功能特性

```typescript
// 响应式布局支持
// 小屏和中屏：底部标签栏导航
// 大屏：侧边栏导航

// 标签页配置
const tabs = [
  { id: TabBarType.HOME, view: ComponentListView },      // 首页
  { id: TabBarType.SAMPLE, view: PracticesView },        // 安全实践
  { id: TabBarType.PRACTICE, view: ExplorationView },    // 社区探索
  { id: TabBarType.MINE, view: MineView }                // 个人中心
];

// 双击退出功能
private handleBackPress(): boolean {
  const currentTime = new Date().getTime();
  if (currentTime - this.backPressTime > PRESS_TIME) {
    this.backPressTime = currentTime;
    promptAction.showToast({ message: $r('app.string.press_again_to_exit') });
    return true;
  }
  return false;
}
```

### 3. 启动页面 (SplashPage)

应用的启动页面，负责初始化和资源预加载。

```typescript
import { SplashPage } from './page/SplashPage';

// 启动页功能：
// - 显示启动动画
// - 检查首次启动
// - 预加载资源
// - 跳转到主页面
// - 状态栏颜色设置

// 使用示例
@Component
struct SplashContainer {
  build() {
    SplashPage()
  }
}
```

#### 启动页视图模型

```typescript
import { 
  SplashViewModel, 
  SplashEventTypeEnum 
} from './viewmodel/SplashViewModel';

class SplashManager {
  private viewModel: SplashViewModel = new SplashViewModel();

  // 检查首次启动
  checkFirstStart(): void {
    this.viewModel.sendEvent(SplashEventTypeEnum.CHECK_FIRST_START);
  }

  // 预加载资源
  preloadResources(): void {
    this.viewModel.sendEvent(SplashEventTypeEnum.PRELOAD_RESOURCES);
  }

  // 跳转到主页面
  jumpToMain(): void {
    this.viewModel.sendEvent(SplashEventTypeEnum.JUMP_TO_MAIN);
  }
}
```

### 4. 自定义标签栏 (CustomTabBar)

响应式的底部标签栏组件，支持多断点适配。

```typescript
import { CustomTabBar } from './component/CustomTabBar';

@Component
struct TabBarContainer {
  @State currentIndex: number = 0;

  build() {
    Column() {
      // 内容区域
      this.ContentArea()
      
      // 自定义标签栏
      CustomTabBar({
        currentIndex: this.currentIndex,
        tabBarChange: (index: number) => {
          this.currentIndex = index;
          this.handleTabChange(index);
        }
      })
    }
  }

  private handleTabChange(index: number) {
    // 处理标签切换逻辑
    console.log('Tab changed to:', index);
  }

  @Builder
  ContentArea() {
    // 内容区域构建器
    Text('Content Area')
  }
}
```

### 5. 自定义侧边栏 (CustomSideBar)

大屏设备的侧边导航栏组件。

```typescript
import { CustomSideBar } from './component/CustomSideBar';

@Component
struct SideBarContainer {
  @State currentIndex: number = 0;
  @State showSideBar: boolean = false;

  build() {
    SideBarContainer(SideBarContainerType.Embed) {
      // 侧边栏内容
      CustomSideBar({
        currentIndex: this.currentIndex,
        sideBarChange: (index: number) => {
          this.currentIndex = index;
          this.handleSideBarChange(index);
        }
      })

      // 主内容区域
      this.MainContent()
    }
    .showSideBar(this.showSideBar)
    .showControlButton(false)
  }

  private handleSideBarChange(index: number) {
    // 处理侧边栏切换逻辑
    console.log('SideBar changed to:', index);
  }

  @Builder
  MainContent() {
    // 主内容区域构建器
    Text('Main Content')
  }
}
```

### 6. 标签栏数据模型 (TabBarModel)

定义标签栏的数据结构和配置。

```typescript
import { 
  TabBarData, 
  TABS_LIST, 
  TabBarType 
} from './model/TabBarModel';

// 标签栏数据接口
interface TabBarData {
  id: TabBarType;
  title: ResourceStr;
  icon: Resource;
}

// 使用预定义的标签列表
const tabsList: TabBarData[] = TABS_LIST;

// 自定义标签配置
const customTabs: TabBarData[] = [
  {
    id: TabBarType.HOME,
    icon: $r('sys.symbol.house_fill'),
    title: $r('app.string.tab_home')
  },
  {
    id: TabBarType.SAMPLE,
    icon: $r('sys.symbol.key_shield_fill'),
    title: $r('app.string.tab_security')
  },
  {
    id: TabBarType.PRACTICE,
    icon: $r('sys.symbol.discover_fill'),
    title: $r('app.string.tab_community')
  },
  {
    id: TabBarType.MINE,
    icon: $r('sys.symbol.person_crop_circle_fill_1'),
    title: $r('app.string.tab_mine')
  }
];
```

### 7. 备份恢复能力 (EntryBackupAbility)

应用数据的备份和恢复功能。

```typescript
import EntryBackupAbility from './entrybackupability/EntryBackupAbility';

// 备份恢复能力自动处理：
// - 应用数据备份
// - 应用数据恢复
// - 版本兼容性检查
// - 备份状态管理

// 备份操作示例
class BackupManager {
  // 触发备份操作
  async triggerBackup(): Promise<void> {
    try {
      // 系统会自动调用EntryBackupAbility.onBackup()
      console.log('Backup operation triggered');
    } catch (error) {
      console.error('Backup failed:', error);
    }
  }

  // 触发恢复操作
  async triggerRestore(): Promise<void> {
    try {
      // 系统会自动调用EntryBackupAbility.onRestore()
      console.log('Restore operation triggered');
    } catch (error) {
      console.error('Restore failed:', error);
    }
  }
}
```

### 8. 表单能力 (PhoneFormAbility)

手机表单小组件功能。

```typescript
import PhoneFormAbility from './phoneformability/PhoneFormAbility';

// 表单能力提供：
// - 桌面小组件支持
// - 表单生命周期管理
// - 表单数据更新
// - 表单事件处理
```

### 9. 响应式设计支持

应用支持多种设备形态和屏幕尺寸：

```typescript
// 设备类型支持
const deviceTypes = ['phone', 'tablet', '2in1'];

// 断点配置
enum BreakpointType {
  SM = 'sm',    // 小屏幕 (< 600dp)
  MD = 'md',    // 中屏幕 (600dp - 840dp)
  LG = 'lg',    // 大屏幕 (840dp - 1440dp)
  XL = 'xl'     // 超大屏幕 (> 1440dp)
}

// 响应式布局示例
@Component
struct ResponsiveLayout {
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = 
    AppStorage.get('GlobalInfoModel')!;

  build() {
    Column() {
      // 根据断点显示不同布局
      if (this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL) {
        // 大屏布局：侧边栏 + 内容区域
        this.LargeScreenLayout()
      } else {
        // 小屏布局：标签栏 + 内容区域
        this.SmallScreenLayout()
      }
    }
  }

  @Builder
  LargeScreenLayout() {
    Row() {
      // 侧边栏
      CustomSideBar({ /* props */ })
        .width(240)
      
      // 内容区域
      Column() {
        // 主要内容
      }
      .layoutWeight(1)
    }
  }

  @Builder
  SmallScreenLayout() {
    Column() {
      // 内容区域
      Column() {
        // 主要内容
      }
      .layoutWeight(1)
      
      // 底部标签栏
      CustomTabBar({ /* props */ })
    }
  }
}
```

### 10. 权限管理

应用所需的系统权限配置：

```json
{
  "requestPermissions": [
    {
      "name": "ohos.permission.INTERNET",
      "reason": "$string:internet_reason",
      "usedScene": {
        "abilities": ["EntryAbility"],
        "when": "inuse"
      }
    },
    {
      "name": "ohos.permission.GET_NETWORK_INFO",
      "reason": "$string:network_reason",
      "usedScene": {
        "abilities": ["EntryAbility"],
        "when": "inuse"
      }
    },
    {
      "name": "ohos.permission.VIBRATE",
      "reason": "$string:vibrator_reason",
      "usedScene": {
        "abilities": ["EntryAbility"],
        "when": "inuse"
      }
    }
  ]
}
```

### 11. 多窗口支持

应用支持多窗口和自由窗口模式：

```typescript
// 窗口配置
const windowConfig = {
  preferMultiWindowOrientation: 'landscape_auto',
  minWindowWidth: 1440,
  minWindowHeight: 940
};

// 窗口管理示例
class WindowManager {
  // 请求全屏显示
  requestFullScreen(windowStage: window.WindowStage): void {
    WindowUtil.requestFullScreen(windowStage, this.context);
  }

  // 注册断点监听
  registerBreakPoint(windowStage: window.WindowStage): void {
    WindowUtil.registerBreakPoint(windowStage);
  }

  // 隐藏标题栏
  hideTitleBar(windowStage: window.WindowStage): void {
    WindowUtil.hideTitleBar(windowStage);
  }

  // 更新状态栏颜色
  updateStatusBarColor(isDark: boolean): void {
    WindowUtil.updateStatusBarColor(getContext(), isDark);
  }
}
```

## 模块依赖

该模块集成了以下功能模块：

```json
{
  "dependencies": {
    "@ohos/home": "file:../../Features/Home",
    "@ohos/security": "file:../../Features/Security",
    "@ohos/community": "file:../../Features/Community",
    "@ohos/mine": "file:../../Features/Mine",
    "@ohos/commonbusiness": "file:../../Features/CommonBusiness",
    "@ohos/common": "file:../../Common"
  }
}
```

## 安装和配置

1. 确保所有依赖模块已正确安装
2. 配置模块权限和能力
3. 设置启动页面和路由
4. 配置资源文件和主题

## 注意事项

1. **设备适配**：支持手机、平板、2in1设备的多形态适配
2. **响应式设计**：根据屏幕尺寸自动切换布局模式
3. **权限管理**：需要网络、网络信息、振动等权限
4. **多窗口支持**：支持自由窗口和多窗口模式
5. **备份恢复**：提供完整的数据备份和恢复功能
6. **表单小组件**：支持桌面小组件功能
7. **生命周期管理**：完整的应用生命周期管理
8. **状态管理**：全局状态管理和页面上下文管理

## 版本信息

- 版本：1.0.0
- 模块类型：entry（入口模块）
- 主入口：EntryAbility
- 支持设备：phone, tablet, 2in1

该模块作为HarmonyOS应用的主入口，提供了完整的应用框架和多模块集成能力，为用户提供统一、流畅的移动应用体验。
