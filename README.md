# Almighty - HarmonyOS全能应用开发框架

## 项目概述

Almighty是一个基于HarmonyOS的全能应用开发框架，采用模块化架构设计，为开发者提供了完整的移动应用开发解决方案。该项目集成了组件展示、安全实践、社区探索、个人中心等多个功能模块，支持手机、平板、2in1设备的多形态适配，是学习和开发HarmonyOS应用的理想平台。

## 项目特色

- 🏗️ **模块化架构** - 清晰的模块分离，便于维护和扩展
- 📱 **多设备适配** - 支持手机、平板、2in1设备的响应式设计
- 🎨 **丰富组件库** - 30+精美UI组件展示和代码预览
- 🔒 **安全实践** - 完整的安全编程实践和最佳实践展示
- 🌐 **社区功能** - 开发者社区探索和经验分享
- ⚙️ **个人中心** - 完整的个人信息管理和应用设置
- 🎯 **响应式布局** - 基于断点系统的自适应布局
- 🔧 **开发工具** - 完整的开发工具链和调试支持

## 项目架构

```
Almighty/
├── Common/                   # 基础公共模块
│   ├── src/main/ets/
│   │   ├── component/        # 通用UI组件
│   │   ├── constant/         # 常量定义
│   │   ├── model/            # 数据模型
│   │   ├── service/          # 服务层
│   │   └── util/             # 工具类
│   └── README.md
├── Features/                 # 功能模块目录
│   ├── CommonBusiness/       # 通用业务模块
│   │   ├── src/main/ets/
│   │   │   ├── component/    # 业务组件
│   │   │   ├── model/        # 业务模型
│   │   │   └── service/      # 业务服务
│   │   └── README.md
│   ├── Home/                 # 首页组件展示模块
│   │   ├── src/main/ets/
│   │   │   ├── component/    # 组件展示
│   │   │   ├── model/        # 组件数据
│   │   │   ├── service/      # 组件服务
│   │   │   └── view/         # 页面视图
│   │   └── README.md
│   ├── Security/             # 安全实践模块
│   │   ├── src/main/ets/
│   │   │   ├── component/    # 安全组件
│   │   │   ├── model/        # 安全数据
│   │   │   ├── service/      # 安全服务
│   │   │   └── view/         # 安全视图
│   │   └── README.md
│   ├── Community/            # 社区探索模块
│   │   ├── src/main/ets/
│   │   │   ├── component/    # 社区组件
│   │   │   ├── model/        # 社区数据
│   │   │   ├── service/      # 社区服务
│   │   │   └── view/         # 社区视图
│   │   └── README.md
│   └── Mine/                 # 个人中心模块
│       ├── src/main/ets/
│       │   ├── component/    # 个人组件
│       │   ├── model/        # 个人数据
│       │   ├── viewmodel/    # 视图模型
│       │   └── view/         # 个人视图
│       └── README.md
├── Multiverse/               # 多设备适配目录
│   └── Phone/                # 手机应用主模块
│       ├── src/main/
│       │   ├── ets/
│       │   │   ├── component/      # 主应用组件
│       │   │   ├── entryability/   # 入口能力
│       │   │   ├── page/           # 主要页面
│       │   │   └── viewmodel/      # 视图模型
│       │   ├── module.json5        # 模块配置
│       │   └── resources/          # 资源文件
│       ├── oh-package.json5        # 包配置
│       └── README.md
├── build-profile.json5       # 构建配置
├── hvigorfile.ts            # 构建脚本
├── oh-package.json5         # 项目包配置
└── README.md                # 项目说明文档
```

## 核心模块介绍

### 🏠 Common - 基础公共模块
提供整个应用的基础设施，包括：
- **工具类**：WindowUtil、Logger、PreferenceManager等
- **基础组件**：TopNavigationView、LoadingComponent等
- **断点系统**：BreakpointSystem响应式布局支持
- **全局管理**：GlobalContext、ProcessUtil等

### 🏢 CommonBusiness - 通用业务模块
提供可复用的业务组件，包括：
- **卡片组件**：BannerCard、DetailCard等
- **导航组件**：FullScreenNavigation、BaseHomeView等
- **数据模型**：CardData、BannerData等
- **业务服务**：通用业务逻辑处理

### 🏡 Home - 首页组件展示模块
展示30+精美UI组件，包括：
- **组件库**：Button、Text、Image、List等组件展示
- **代码预览**：实时代码查看和复制功能
- **交互演示**：组件属性调整和效果预览
- **学习指南**：组件使用说明和最佳实践

### 🔐 Security - 安全实践模块
提供安全编程实践，包括：
- **安全示例**：加密、认证、权限等安全实践
- **最佳实践**：安全编程指南和规范
- **代码演示**：安全相关的代码示例
- **学习资源**：安全知识和实践案例

### 🌍 Community - 社区探索模块
提供开发者社区功能，包括：
- **动态卡片**：开发者动态和分享
- **经验分享**：技术文章和经验交流
- **开发者内容**：社区精选内容展示
- **互动功能**：点赞、评论、分享等

### 👤 Mine - 个人中心模块
提供个人信息管理，包括：
- **个人设置**：应用配置和个人偏好
- **关于页面**：应用信息和版本管理
- **版本检查**：自动检查和更新提醒
- **反馈功能**：用户反馈和建议

### 📱 Phone - 手机应用主模块
应用的主入口模块，包括：
- **应用入口**：EntryAbility生命周期管理
- **主页面**：MainPage导航和布局
- **启动页面**：SplashPage启动动画
- **响应式导航**：CustomTabBar和CustomSideBar

## 技术特性

### 响应式设计
- **断点系统**：SM(小屏)、MD(中屏)、LG(大屏)、XL(超大屏)
- **自适应布局**：根据屏幕尺寸自动调整布局
- **多设备支持**：手机、平板、2in1设备适配

### 架构模式
- **MVVM架构**：Model-View-ViewModel分层架构
- **模块化设计**：功能模块独立，便于维护
- **组件化开发**：可复用组件库
- **事件驱动**：基于事件的状态管理

### 开发工具
- **TypeScript/ArkTS**：强类型语言支持
- **DevEco Studio**：官方IDE开发环境
- **HarmonyOS SDK**：原生API支持
- **模块化构建**：ohpm包管理

## 快速开始

### 环境要求
- DevEco Studio 4.0+
- HarmonyOS SDK API 10+
- Node.js 16.0+
- ohpm 包管理器

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd Almighty
```

2. **安装依赖**
```bash
ohpm install
```

3. **构建项目**
```bash
hvigor build
```

4. **运行应用**
```bash
hvigor run
```

### 开发指南

1. **模块开发**
   - 每个功能模块都有独立的README文档
   - 遵循模块化开发规范
   - 使用统一的代码风格

2. **组件开发**
   - 基于ArkTS/ETS语法
   - 使用@Component装饰器
   - 支持响应式状态管理

3. **样式规范**
   - 使用系统资源和主题
   - 支持深色/浅色主题切换
   - 遵循HarmonyOS设计规范

## 模块文档

每个模块都有详细的README文档，包含使用说明、API参考和示例代码：

- [Common模块文档](Common/README.md) - 基础公共模块
- [CommonBusiness模块文档](Features/CommonBusiness/README.md) - 通用业务模块
- [Home模块文档](Features/Home/README.md) - 首页组件展示模块
- [Security模块文档](Features/Security/README.md) - 安全实践模块
- [Community模块文档](Features/Community/README.md) - 社区探索模块
- [Mine模块文档](Features/Mine/README.md) - 个人中心模块
- [Phone模块文档](Multiverse/Phone/README.md) - 手机应用主模块

## 功能演示

### 首页组件展示
- 30+精美UI组件
- 实时代码预览
- 属性调整演示
- 响应式布局展示

### 安全实践学习
- 数据加密示例
- 权限管理实践
- 网络安全指南
- 安全编程规范

### 社区探索体验
- 开发者动态
- 技术文章分享
- 经验交流互动
- 精选内容推荐

### 个人中心管理
- 应用设置配置
- 版本信息查看
- 自动更新检查
- 用户反馈提交

## 贡献指南

我们欢迎社区贡献！请遵循以下步骤：

1. Fork项目仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

### 代码规范
- 遵循TypeScript/ArkTS编码规范
- 使用有意义的变量和函数命名
- 添加必要的注释和文档
- 确保代码通过所有测试

## 许可证

本项目采用Apache-2.0许可证 - 查看[LICENSE](LICENSE)文件了解详情

## 联系我们

- 项目主页：[GitHub Repository]
- 问题反馈：[Issues]
- 技术讨论：[Discussions]
- 邮箱联系：[Email]

## 致谢

感谢所有为这个项目做出贡献的开发者和社区成员！

---

**Almighty** - 让HarmonyOS应用开发变得更简单、更高效！
